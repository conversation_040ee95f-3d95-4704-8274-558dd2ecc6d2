<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:fillViewport="true">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Loading Progress -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="visible" />

        <!-- Error Text -->
        <TextView
            android:id="@+id/error_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:padding="@dimen/margin_large"
            android:textColor="@color/error"
            android:textSize="@dimen/text_size_medium"
            android:visibility="gone"
            tools:text="Error loading order data"
            tools:visibility="gone" />

        <!-- Content Layout -->
        <LinearLayout
            android:id="@+id/content_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/margin_medium"
            android:visibility="gone"
            tools:visibility="visible">

            <!-- Order Status Progress -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/margin_medium"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/elevation_card">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/margin_medium">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/order_progress"
                        android:textColor="@color/text_primary"
                        android:textSize="@dimen/text_size_large"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="@dimen/margin_small"
                        android:layout_marginBottom="@dimen/margin_medium"
                        android:background="@color/divider" />

                    <!-- Status Progress Indicators -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <!-- Placed -->
                        <View
                            android:id="@+id/status_placed_indicator"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:background="@drawable/status_indicator_inactive" />

                        <!-- Line from Placed to Confirmed -->
                        <View
                            android:id="@+id/status_placed_line"
                            android:layout_width="0dp"
                            android:layout_height="2dp"
                            android:layout_weight="1"
                            android:background="@color/text_color" />

                        <!-- Confirmed -->
                        <View
                            android:id="@+id/status_confirmed_indicator"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:background="@drawable/status_indicator_inactive" />

                        <!-- Line from Confirmed to Pickup Scheduled -->
                        <View
                            android:id="@+id/status_confirmed_line"
                            android:layout_width="0dp"
                            android:layout_height="2dp"
                            android:layout_weight="1"
                            android:background="@color/text_color" />

                        <!-- Pickup Scheduled -->
                        <View
                            android:id="@+id/status_pickup_scheduled_indicator"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:background="@drawable/status_indicator_inactive" />

                        <!-- Line from Pickup Scheduled to Picked Up -->
                        <View
                            android:id="@+id/status_pickup_scheduled_line"
                            android:layout_width="0dp"
                            android:layout_height="2dp"
                            android:layout_weight="1"
                            android:background="@color/text_color" />

                        <!-- Picked Up -->
                        <View
                            android:id="@+id/status_picked_up_indicator"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:background="@drawable/status_indicator_inactive" />

                        <!-- Line from Picked Up to Processing (added to fix missing reference) -->
                        <View
                            android:id="@+id/status_picked_up_line"
                            android:layout_width="0dp"
                            android:layout_height="2dp"
                            android:layout_weight="1"
                            android:background="@color/text_color" />
                    </LinearLayout>

                    <!-- Status Labels Row 1 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="@dimen/margin_extra_small">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/status_placed"
                            android:textSize="@dimen/text_size_micro"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/status_confirmed"
                            android:textSize="@dimen/text_size_micro"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/status_pickup_scheduled"
                            android:textSize="@dimen/text_size_micro"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/status_picked_up"
                            android:textSize="@dimen/text_size_micro"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />
                    </LinearLayout>

                    <!-- Status Progress Indicators Row 2 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginTop="@dimen/margin_medium">

                        <!-- Processing -->
                        <View
                            android:id="@+id/status_processing_indicator"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:background="@drawable/status_indicator_inactive" />

                        <!-- Line from Processing to Ready -->
                        <View
                            android:id="@+id/status_processing_line"
                            android:layout_width="0dp"
                            android:layout_height="2dp"
                            android:layout_weight="1"
                            android:background="@color/text_color" />

                        <!-- Ready for Delivery -->
                        <View
                            android:id="@+id/status_ready_indicator"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:background="@drawable/status_indicator_inactive" />

                        <!-- Line from Ready to Out for Delivery -->
                        <View
                            android:id="@+id/status_ready_line"
                            android:layout_width="0dp"
                            android:layout_height="2dp"
                            android:layout_weight="1"
                            android:background="@color/text_color" />

                        <!-- Out for Delivery -->
                        <View
                            android:id="@+id/status_out_for_delivery_indicator"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:background="@drawable/status_indicator_inactive" />

                        <!-- Line from Out for Delivery to Delivered -->
                        <View
                            android:id="@+id/status_out_for_delivery_line"
                            android:layout_width="0dp"
                            android:layout_height="2dp"
                            android:layout_weight="1"
                            android:background="@color/text_color" />

                        <!-- Delivered -->
                        <View
                            android:id="@+id/status_delivered_indicator"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:background="@drawable/status_indicator_inactive" />
                    </LinearLayout>

                    <!-- Status Labels Row 2 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="@dimen/margin_extra_small">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/status_processing"
                            android:textSize="@dimen/text_size_micro"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/status_ready"
                            android:textSize="@dimen/text_size_micro"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/status_out_for_delivery"
                            android:textSize="@dimen/text_size_micro"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/status_delivered"
                            android:textSize="@dimen/text_size_micro"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />
                    </LinearLayout>

                    <!-- Cancelled Status (Only shown when order is cancelled) -->
                    <TextView
                        android:id="@+id/status_cancelled_indicator"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_medium"
                        android:background="@drawable/bg_cancelled_status"
                        android:gravity="center"
                        android:padding="@dimen/margin_small"
                        android:text="@string/status_cancelled"
                        android:textColor="@color/white"
                        android:textSize="@dimen/text_size_medium"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="gone" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Order Details Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/order_details_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/margin_medium"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/elevation_card">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/margin_medium">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/order_details"
                        android:textColor="@color/text_primary"
                        android:textSize="@dimen/text_size_large"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="@dimen/margin_small"
                        android:layout_marginBottom="@dimen/margin_medium"
                        android:background="@color/divider" />

                    <!-- Order Number -->
                    <TextView
                        android:id="@+id/order_number"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_primary"
                        android:textSize="@dimen/text_size_medium"
                        android:textStyle="bold"
                        tools:text="Order #ORD2505218362" />

                    <!-- Order Date -->
                    <TextView
                        android:id="@+id/order_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_small"
                        android:textColor="@color/text_secondary"
                        android:textSize="@dimen/text_size_small"
                        tools:text="Order Date: 2025-05-22 02:54:37" />

                    <!-- Order Status -->
                    <TextView
                        android:id="@+id/order_status"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_small"
                        android:textColor="@color/info"
                        android:textSize="@dimen/text_size_small"
                        android:textStyle="bold"
                        tools:text="Status: Placed" />

                    <!-- Order Total -->
                    <TextView
                        android:id="@+id/order_total"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_small"
                        android:textColor="@color/text_primary"
                        android:textSize="@dimen/text_size_medium"
                        android:textStyle="bold"
                        tools:text="Total: ₹69.0" />

                    <!-- Customer Details -->
                    <TextView
                        android:id="@+id/customer_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_medium"
                        android:textColor="@color/text_primary"
                        android:textSize="@dimen/text_size_small"
                        tools:text="Customer: John Doe" />

                    <TextView
                        android:id="@+id/customer_phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_extra_small"
                        android:textColor="@color/text_secondary"
                        android:textSize="@dimen/text_size_small"
                        tools:text="Phone: +91 9876543210" />

                    <!-- Payment Details -->
                    <TextView
                        android:id="@+id/payment_method"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_medium"
                        android:textColor="@color/text_primary"
                        android:textSize="@dimen/text_size_small"
                        tools:text="Payment Method: Cash on Delivery" />

                    <TextView
                        android:id="@+id/payment_status"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_extra_small"
                        android:textColor="@color/text_secondary"
                        android:textSize="@dimen/text_size_small"
                        tools:text="Payment Status: Pending" />

                    <!-- Delivery Address -->
                    <TextView
                        android:id="@+id/delivery_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_medium"
                        android:textColor="@color/text_primary"
                        android:textSize="@dimen/text_size_small"
                        tools:text="Delivery Address: 123 Main St, Anytown, Anystate" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Order Items Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/order_items_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/margin_medium"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/elevation_card">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/margin_medium">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/order_items"
                        android:textColor="@color/text_primary"
                        android:textSize="@dimen/text_size_large"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="@dimen/margin_small"
                        android:layout_marginBottom="@dimen/margin_medium"
                        android:background="@color/divider" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/order_items_recycler_view"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:itemCount="3"
                        tools:listitem="@layout/item_order_item" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Order Status History Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/order_status_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/margin_medium"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/elevation_card">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/margin_medium">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/status_history"
                        android:textColor="@color/text_primary"
                        android:textSize="@dimen/text_size_large"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="@dimen/margin_small"
                        android:layout_marginBottom="@dimen/margin_medium"
                        android:background="@color/divider" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/status_history_recycler_view"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:itemCount="3"
                        tools:listitem="@layout/item_status_history" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </FrameLayout>
</androidx.core.widget.NestedScrollView>
