<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Glass background with transparency -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#26FFFFFF" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
    <!-- Border stroke for glass effect -->
    <item>
        <shape android:shape="rectangle">
            <stroke 
                android:width="1dp" 
                android:color="#33FFFFFF" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
    <!-- Inner highlight for depth -->
    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
        <shape android:shape="rectangle">
            <stroke 
                android:width="0.5dp" 
                android:color="#66FFFFFF" />
            <corners android:radius="19dp" />
        </shape>
    </item>
    
</layer-list>
