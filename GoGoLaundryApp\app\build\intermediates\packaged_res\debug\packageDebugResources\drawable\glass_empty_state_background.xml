<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Background with subtle gradient -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#12FFFFFF"
                android:endColor="#08FFFFFF"
                android:angle="135" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
    <!-- Glass effect overlay -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#15FFFFFF" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
    <!-- Subtle border -->
    <item>
        <shape android:shape="rectangle">
            <stroke
                android:width="1dp"
                android:color="#20FFFFFF" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
    <!-- Inner glow effect -->
    <item android:insetLeft="1dp" android:insetTop="1dp" android:insetRight="1dp" android:insetBottom="1dp">
        <shape android:shape="rectangle">
            <stroke
                android:width="0.5dp"
                android:color="#30FFFFFF" />
            <corners android:radius="19dp" />
        </shape>
    </item>
    
</layer-list>
