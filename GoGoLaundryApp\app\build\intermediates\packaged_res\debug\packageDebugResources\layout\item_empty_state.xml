<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="40dp"
    android:background="@color/background_dark_new">

    <ImageView
        android:id="@+id/emptyIcon"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:src="@drawable/ic_empty_box"
        app:tint="@color/text_secondary_dark"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/emptyText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="No items available"
        android:textColor="@color/text_secondary_dark"
        android:textSize="16sp"
        android:textStyle="italic"
        android:gravity="center" />

</LinearLayout>
