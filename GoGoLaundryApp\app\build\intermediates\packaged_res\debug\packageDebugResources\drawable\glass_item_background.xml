<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Shadow effect -->
    <item android:top="2dp" android:left="2dp">
        <shape android:shape="rectangle">
            <solid android:color="#10000000" />
            <corners android:radius="14dp" />
        </shape>
    </item>
    
    <!-- Main glass background -->
    <item android:bottom="2dp" android:right="2dp">
        <shape android:shape="rectangle">
            <solid android:color="#33FFFFFF" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
    <!-- Border for glass effect -->
    <item android:bottom="2dp" android:right="2dp">
        <shape android:shape="rectangle">
            <stroke 
                android:width="1dp" 
                android:color="#4DFFFFFF" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
    <!-- Inner highlight -->
    <item android:top="1dp" android:left="1dp" android:right="3dp" android:bottom="3dp">
        <shape android:shape="rectangle">
            <stroke 
                android:width="0.5dp" 
                android:color="#66FFFFFF" />
            <corners android:radius="11dp" />
        </shape>
    </item>
    
</layer-list>
