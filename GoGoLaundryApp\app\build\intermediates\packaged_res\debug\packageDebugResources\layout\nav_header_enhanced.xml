<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="200dp"
    android:background="@drawable/nav_header_background"
    android:padding="16dp"
    android:theme="@style/ThemeOverlay.MaterialComponents.Dark">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/profile_image"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginTop="21dp"
        android:padding="2dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_person"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/CircleImageView"
        app:strokeColor="@android:color/white"
        app:strokeWidth="2dp" />

    <ImageView
        android:id="@+id/edit_profile_image"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:background="@drawable/circle_background"
        android:padding="4dp"
        android:src="@drawable/ic_edit"
        app:layout_constraintBottom_toBottomOf="@id/profile_image"
        app:layout_constraintEnd_toEndOf="@id/profile_image"
        app:tint="@android:color/white" />

    <TextView
        android:id="@+id/nav_header_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="User Name"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
        android:textColor="@android:color/white"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/profile_image" />

    <TextView
        android:id="@+id/nav_header_phone"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="+8801XXXXXXXXX"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
        android:textColor="@android:color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/nav_header_name" />

    <TextView
        android:id="@+id/nav_header_email"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="<EMAIL>"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
        android:textColor="@android:color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/nav_header_phone" />

</androidx.constraintlayout.widget.ConstraintLayout>
