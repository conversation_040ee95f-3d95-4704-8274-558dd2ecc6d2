<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Shadow layer -->
    <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="2dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/picker_card_shadow" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
    <!-- Main background with glassmorphism effect -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="135"
                android:startColor="@color/glass_background"
                android:endColor="@color/picker_card_background" />
            <corners android:radius="16dp" />
            <stroke
                android:width="1dp"
                android:color="@color/glass_border" />
        </shape>
    </item>
    
    <!-- Subtle inner glow -->
    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="45"
                android:startColor="#08FFFFFF"
                android:centerColor="#00FFFFFF"
                android:endColor="#08FFFFFF" />
            <corners android:radius="15dp" />
        </shape>
    </item>
</layer-list>
