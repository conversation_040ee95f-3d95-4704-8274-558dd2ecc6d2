<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@color/background_light">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="16dp">

        <!-- Modern Hero Section -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/hero_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            app:cardBackgroundColor="@color/primary"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="24dp">

                <!-- Welcome Section -->
                <TextView
                    android:id="@+id/welcome_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Hello!"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline4"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:letterSpacing="0.02"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/welcome_subtitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Ready for fresh laundry today?"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                    android:textColor="@color/white"
                    android:alpha="0.9"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/welcome_text" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Content Section -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/content_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintTop_toBottomOf="@id/hero_section">



            <!-- Recent Orders Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/recent_orders_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp"
                app:strokeColor="@color/card_stroke_light"
                app:strokeWidth="0.5dp"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="20dp">

                    <!-- Recent Orders Header -->
                    <LinearLayout
                        android:id="@+id/recent_orders_header"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        app:layout_constraintTop_toTopOf="parent">

                        <!-- Header Icon -->
                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginEnd="12dp"
                            app:cardBackgroundColor="@color/primary_light"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="0dp">

                            <ImageView
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_receipt"
                                app:tint="@color/primary" />

                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:id="@+id/recent_orders_title"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Recent Orders"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <!-- View All Button -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/view_all_orders"
                            style="@style/Widget.Material3.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="View All"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                            android:textColor="@color/primary"
                            android:textStyle="bold"
                            app:cornerRadius="20dp"
                            app:icon="@drawable/ic_arrow_forward"
                            app:iconGravity="end"
                            app:iconSize="16dp"
                            app:iconTint="@color/primary" />

                    </LinearLayout>

                    <!-- Recent Orders Description -->
                    <TextView
                        android:id="@+id/recent_orders_subtitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:layout_marginStart="44dp"
                        android:text="Track your recent laundry orders"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                        android:textColor="@color/text_secondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/recent_orders_header" />

                    <!-- Recent Orders Container -->
                    <FrameLayout
                        android:id="@+id/recent_orders_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        app:layout_constraintTop_toBottomOf="@id/recent_orders_subtitle">

                        <!-- Recent Orders Shimmer Effect -->
                        <include
                            android:id="@+id/recent_orders_shimmer"
                            layout="@layout/layout_recent_orders_shimmer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="visible" />

                        <!-- Recent Orders RecyclerView -->
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recent_orders_recycler_view"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:orientation="horizontal"
                            android:paddingStart="4dp"
                            android:paddingEnd="4dp"
                            android:nestedScrollingEnabled="false"
                            android:visibility="gone"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            tools:itemCount="3"
                            tools:listitem="@layout/item_recent_order" />

                        <!-- Empty State for Recent Orders -->
                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/recent_orders_empty_state"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:layout_marginBottom="8dp"
                            android:background="@color/background_light"
                            android:padding="32dp"
                            android:visibility="gone">

                            <!-- Empty State Icon -->
                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/empty_icon_container"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                app:cardBackgroundColor="@color/primary_light"
                                app:cardCornerRadius="40dp"
                                app:cardElevation="2dp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <ImageView
                                    android:layout_width="40dp"
                                    android:layout_height="40dp"
                                    android:layout_gravity="center"
                                    android:src="@drawable/ic_receipt"
                                    app:tint="@color/primary" />

                            </com.google.android.material.card.MaterialCardView>

                            <!-- Empty State Title -->
                            <TextView
                                android:id="@+id/empty_title"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="24dp"
                                android:text="No Recent Orders"
                                android:textAlignment="center"
                                android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                                android:textColor="@color/text_primary"
                                android:textStyle="bold"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/empty_icon_container" />

                            <!-- Empty State Subtitle -->
                            <TextView
                                android:id="@+id/empty_subtitle"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="Start your first order to see it here"
                                android:textAlignment="center"
                                android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                                android:textColor="@color/text_secondary"
                                android:lineSpacingExtra="2dp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/empty_title" />

                            <!-- Call to Action Button -->
                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_start_order"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="24dp"
                                android:text="Start Order"
                                android:textColor="@color/white"
                                android:textStyle="bold"
                                app:backgroundTint="@color/primary"
                                app:cornerRadius="25dp"
                                app:icon="@drawable/ic_add"
                                app:iconTint="@color/white"
                                app:iconGravity="textStart"
                                app:iconPadding="8dp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/empty_subtitle" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </FrameLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Popular Services Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/services_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp"
                app:strokeColor="@color/card_stroke_light"
                app:strokeWidth="0.5dp"
                app:layout_constraintTop_toBottomOf="@id/recent_orders_card">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="20dp">

                    <!-- Services Header -->
                    <LinearLayout
                        android:id="@+id/services_header"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        app:layout_constraintTop_toTopOf="parent">

                        <!-- Header Icon -->
                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginEnd="12dp"
                            app:cardBackgroundColor="@color/primary_light"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="0dp">

                            <ImageView
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_services"
                                app:tint="@color/primary" />

                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:id="@+id/popular_services_title"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Popular Services"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <!-- View All Button -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/view_all_services"
                            style="@style/Widget.Material3.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="View All"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                            android:textColor="@color/primary"
                            android:textStyle="bold"
                            app:cornerRadius="20dp"
                            app:icon="@drawable/ic_arrow_forward"
                            app:iconGravity="end"
                            app:iconSize="16dp"
                            app:iconTint="@color/primary" />

                    </LinearLayout>

                    <!-- Services Description -->
                    <TextView
                        android:id="@+id/services_subtitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:layout_marginStart="44dp"
                        android:text="Choose from our most popular laundry services"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                        android:textColor="@color/text_secondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/services_header" />

                    <!-- Services Container -->
                    <FrameLayout
                        android:id="@+id/services_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        app:layout_constraintTop_toBottomOf="@id/services_subtitle">

                        <!-- Services Shimmer Effect -->
                        <include
                            android:id="@+id/services_shimmer"
                            layout="@layout/layout_services_shimmer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="visible" />

                        <!-- Services RecyclerView -->
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/services_grid"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:nestedScrollingEnabled="false"
                            android:paddingStart="4dp"
                            android:paddingEnd="4dp"
                            android:visibility="gone"
                            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                            app:spanCount="2"
                            tools:itemCount="4"
                            tools:listitem="@layout/item_service_grid" />

                        <!-- Empty State for Services -->
                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/services_empty_state"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:layout_marginBottom="8dp"
                            android:padding="32dp"
                            android:visibility="gone">

                            <!-- Empty State Icon -->
                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/services_empty_icon_container"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                app:cardBackgroundColor="@color/primary_light"
                                app:cardCornerRadius="40dp"
                                app:cardElevation="0dp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <ImageView
                                    android:layout_width="40dp"
                                    android:layout_height="40dp"
                                    android:layout_gravity="center"
                                    android:src="@drawable/ic_services"
                                    app:tint="@color/primary" />

                            </com.google.android.material.card.MaterialCardView>

                            <!-- Empty State Title -->
                            <TextView
                                android:id="@+id/services_empty_title"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="24dp"
                                android:text="No Services Available"
                                android:textAlignment="center"
                                android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                                android:textColor="@color/text_primary"
                                android:textStyle="bold"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/services_empty_icon_container" />

                            <!-- Empty State Subtitle -->
                            <TextView
                                android:id="@+id/services_empty_subtitle"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="Services will appear here once they're available"
                                android:textAlignment="center"
                                android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                                android:textColor="@color/text_secondary"
                                android:lineSpacingExtra="2dp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/services_empty_title" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </FrameLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Popular Items Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/popular_items_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp"
                app:strokeColor="@color/card_stroke_light"
                app:strokeWidth="0.5dp"
                app:layout_constraintTop_toBottomOf="@id/services_card">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="20dp">

                    <!-- Items Header -->
                    <LinearLayout
                        android:id="@+id/items_header"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        app:layout_constraintTop_toTopOf="parent">

                        <!-- Header Icon -->
                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginEnd="12dp"
                            app:cardBackgroundColor="@color/primary_light"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="0dp">

                            <ImageView
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_laundry_items"
                                app:tint="@color/primary" />

                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:id="@+id/popular_items_title"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Popular Items"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <!-- View All Button -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/view_all_items"
                            style="@style/Widget.Material3.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="View All"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                            android:textColor="@color/primary"
                            android:textStyle="bold"
                            app:cornerRadius="20dp"
                            app:icon="@drawable/ic_arrow_forward"
                            app:iconGravity="end"
                            app:iconSize="16dp"
                            app:iconTint="@color/primary" />

                    </LinearLayout>

                    <!-- Items Description -->
                    <TextView
                        android:id="@+id/items_subtitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:layout_marginStart="44dp"
                        android:text="Most requested laundry items by our customers"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                        android:textColor="@color/text_secondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/items_header" />

                    <!-- Popular Items Container -->
                    <FrameLayout
                        android:id="@+id/popular_items_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        app:layout_constraintTop_toBottomOf="@id/items_subtitle">

                        <!-- Popular Items Shimmer Effect -->
                        <include
                            android:id="@+id/popular_items_shimmer"
                            layout="@layout/layout_popular_items_shimmer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="visible" />

                        <!-- Popular Items RecyclerView -->
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/popular_items_recycler_view"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:orientation="horizontal"
                            android:paddingStart="4dp"
                            android:paddingEnd="4dp"
                            android:nestedScrollingEnabled="false"
                            android:visibility="gone"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            tools:itemCount="3"
                            tools:listitem="@layout/item_laundry_item" />

                    </FrameLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
