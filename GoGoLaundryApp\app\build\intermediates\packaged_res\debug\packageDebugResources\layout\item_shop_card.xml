<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:elevation="8dp"
    app:cardCornerRadius="16dp"
    app:cardBackgroundColor="@color/glass_background"
    app:strokeColor="@color/white_20"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- Shop Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Shop Image -->
            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/shopImageView"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginEnd="12dp"
                android:scaleType="centerCrop"
                android:background="@color/white_10"
                app:shapeAppearanceOverlay="@style/CircularImageView"
                tools:src="@drawable/ic_shop_placeholder" />

            <!-- Shop Info -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <!-- Shop Name -->
                <TextView
                    android:id="@+id/shopNameTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:maxLines="1"
                    android:ellipsize="end"
                    tools:text="Clean &amp; Fresh Laundry" />

                <!-- Shop Address -->
                <TextView
                    android:id="@+id/shopAddressTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textColor="@color/white_70"
                    android:textSize="12sp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    tools:text="Dhanmondi, Dhaka" />

                <!-- Rating and Distance Row -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <!-- Rating -->
                    <ImageView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:src="@drawable/ic_star_filled"
                        app:tint="@color/rating_star" />

                    <TextView
                        android:id="@+id/ratingTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        tools:text="4.5" />

                    <TextView
                        android:id="@+id/reviewCountTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="2dp"
                        android:textColor="@color/white_70"
                        android:textSize="10sp"
                        tools:text="(123)" />

                    <!-- Distance -->
                    <View
                        android:layout_width="1dp"
                        android:layout_height="12dp"
                        android:layout_marginHorizontal="8dp"
                        android:background="@color/white_30" />

                    <ImageView
                        android:layout_width="10dp"
                        android:layout_height="10dp"
                        android:src="@drawable/ic_location"
                        app:tint="@color/white_70" />

                    <TextView
                        android:id="@+id/distanceTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:textColor="@color/white_70"
                        android:textSize="10sp"
                        tools:text="2.5 km" />

                </LinearLayout>

            </LinearLayout>

            <!-- Status Badge -->
            <TextView
                android:id="@+id/statusBadge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/badge_open"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:text="@string/open"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- Services Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/services_colon"
                android:textColor="@color/white_70"
                android:textSize="11sp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/servicesRecyclerView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:orientation="horizontal"
                tools:listitem="@layout/item_service_chip" />

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- View Details Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/viewDetailsButton"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="@string/view_details"
                android:textColor="@color/white"
                android:textSize="12sp"
                app:strokeColor="@color/white_30"
                app:cornerRadius="18dp" />

            <!-- Order Now Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/orderNowButton"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="@string/order_now"
                android:textColor="@color/white"
                android:textSize="12sp"
                app:backgroundTint="@color/colorPrimary"
                app:cornerRadius="18dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
