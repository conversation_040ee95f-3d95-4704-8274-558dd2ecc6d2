<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardBackgroundColor="@color/glass_background"
    app:strokeColor="@color/white_20"
    app:strokeWidth="1dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- Item Image -->
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/itemImageView"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginEnd="12dp"
            android:scaleType="centerCrop"
            android:background="@color/white_10"
            app:shapeAppearanceOverlay="@style/RoundedImageView"
            tools:src="@drawable/ic_item_placeholder" />

        <!-- Item Details -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Item Name -->
            <TextView
                android:id="@+id/itemNameTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="Men's Shirt" />

            <!-- Service Category -->
            <TextView
                android:id="@+id/serviceCategoryTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textColor="@color/white_70"
                android:textSize="11sp"
                tools:text="Wash &amp; Fold" />

            <!-- Estimated Time -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:src="@drawable/ic_clock"
                    app:tint="@color/white_70" />

                <TextView
                    android:id="@+id/estimatedTimeTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:textColor="@color/white_70"
                    android:textSize="10sp"
                    tools:text="24 hours" />

            </LinearLayout>

        </LinearLayout>

        <!-- Price and Add Button -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal">

            <!-- Price -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <!-- Original Price (if custom price exists) -->
                <TextView
                    android:id="@+id/originalPriceTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white_50"
                    android:textSize="10sp"
                    android:visibility="gone"
                    tools:text="৳50"
                    tools:visibility="visible" />

                <!-- Current Price -->
                <TextView
                    android:id="@+id/priceTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="৳45" />

            </LinearLayout>

            <!-- Add to Cart Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/addToCartButton"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_marginTop="8dp"
                android:minWidth="80dp"
                android:text="@string/add"
                android:textColor="@color/white"
                android:textSize="11sp"
                app:backgroundTint="@color/colorPrimary"
                app:cornerRadius="16dp"
                app:icon="@drawable/ic_add"
                app:iconSize="14dp"
                app:iconTint="@color/white" />

        </LinearLayout>

    </LinearLayout>

    <!-- Quantity Selector (Initially Hidden) -->
    <LinearLayout
        android:id="@+id/quantityLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginHorizontal="12dp"
        android:layout_marginBottom="12dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:visibility="gone"
        tools:visibility="visible">

        <!-- Decrease Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/decreaseButton"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:minWidth="0dp"
            android:insetTop="0dp"
            android:insetBottom="0dp"
            app:icon="@drawable/ic_remove"
            app:iconSize="16dp"
            app:iconTint="@color/white"
            app:strokeColor="@color/white_30"
            app:cornerRadius="18dp" />

        <!-- Quantity Text -->
        <TextView
            android:id="@+id/quantityTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:minWidth="24dp"
            android:gravity="center"
            tools:text="2" />

        <!-- Increase Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/increaseButton"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:minWidth="0dp"
            android:insetTop="0dp"
            android:insetBottom="0dp"
            app:backgroundTint="@color/colorPrimary"
            app:icon="@drawable/ic_add"
            app:iconSize="16dp"
            app:iconTint="@color/white"
            app:cornerRadius="18dp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
