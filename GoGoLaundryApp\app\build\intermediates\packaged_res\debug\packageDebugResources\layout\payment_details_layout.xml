<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/payment_details_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:visibility="visible">

    <!-- Mobile Payment Layout -->
    <LinearLayout
        android:id="@+id/mobile_payment_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="visible"
        android:background="@drawable/edit_text_background"
        android:padding="12dp"
        android:layout_marginTop="8dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Mobile Banking Details"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/colorPrimary"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Payment Provider"
            android:textSize="14sp" />

        <Spinner
            android:id="@+id/mobile_payment_provider_spinner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/spinner_background"
            android:padding="12dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="Transaction ID (Required)"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/colorAccent" />

        <EditText
            android:id="@+id/transaction_id_edit_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/edit_text_background"
            android:hint="Enter transaction ID"
            android:inputType="text"
            android:padding="12dp"
            android:textColor="@color/text_primary"
            android:textColorHint="@color/text_secondary"
            android:textSize="16sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="Instructions:"
            android:textSize="14sp"
            android:textStyle="italic" />

        <!-- Admin Payment Instructions -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:cardCornerRadius="12dp"
            app:cardBackgroundColor="@color/colorPrimary"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="⚠️ IMPORTANT: Pay to Admin Account Only"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:gravity="center"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/admin_payment_number"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Admin bKash: ***********"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:gravity="center"
                    android:background="@drawable/admin_number_background"
                    android:padding="12dp"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="• Send money to the admin number above\n• Do NOT pay directly to shop owners\n• Include your order number in reference\n• Enter transaction ID below"
                    android:textSize="14sp"
                    android:textColor="@color/white"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <!-- Card Payment Layout -->
    <LinearLayout
        android:id="@+id/card_payment_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone"
        android:background="@drawable/edit_text_background"
        android:padding="12dp"
        android:layout_marginTop="8dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Card Details"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/colorPrimary" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Card Number"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/card_number_edit_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/edit_text_background"
            android:hint="Enter card number"
            android:inputType="number"
            android:maxLength="16"
            android:padding="12dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="Note: This is a demo app. Do not enter real card details."
            android:textColor="@color/colorAccent"
            android:textSize="12sp"
            android:textStyle="italic" />
    </LinearLayout>

    <!-- Cash on Delivery Layout -->
    <LinearLayout
        android:id="@+id/cash_payment_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone"
        android:background="@drawable/edit_text_background"
        android:padding="12dp"
        android:layout_marginTop="8dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Cash on Delivery"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/colorPrimary" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="You will pay when your order is delivered."
            android:textSize="14sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Please have the exact amount ready for the delivery person."
            android:textSize="12sp"
            android:textStyle="italic" />
    </LinearLayout>

</LinearLayout>
