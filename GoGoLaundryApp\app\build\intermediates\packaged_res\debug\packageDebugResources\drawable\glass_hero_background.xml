<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Main glass background -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#40FFFFFF" />
            <corners 
                android:topLeftRadius="0dp"
                android:topRightRadius="0dp"
                android:bottomLeftRadius="24dp"
                android:bottomRightRadius="24dp" />
        </shape>
    </item>
    
    <!-- Border for glass effect -->
    <item>
        <shape android:shape="rectangle">
            <stroke 
                android:width="1dp" 
                android:color="#4DFFFFFF" />
            <corners 
                android:topLeftRadius="0dp"
                android:topRightRadius="0dp"
                android:bottomLeftRadius="24dp"
                android:bottomRightRadius="24dp" />
        </shape>
    </item>
    
    <!-- Inner glow -->
    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
        <shape android:shape="rectangle">
            <stroke 
                android:width="0.5dp" 
                android:color="#80FFFFFF" />
            <corners 
                android:topLeftRadius="0dp"
                android:topRightRadius="0dp"
                android:bottomLeftRadius="23dp"
                android:bottomRightRadius="23dp" />
        </shape>
    </item>
    
</layer-list>
