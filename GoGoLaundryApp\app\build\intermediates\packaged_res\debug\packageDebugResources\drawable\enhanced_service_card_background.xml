<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Gradient background -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#667eea"
                android:endColor="#764ba2"
                android:angle="135"
                android:type="linear" />
            <corners android:radius="@dimen/service_card_corner_radius" />
        </shape>
    </item>

    <!-- Glass overlay -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#40FFFFFF" />
            <corners android:radius="@dimen/service_card_corner_radius" />
        </shape>
    </item>

    <!-- Border for glass effect -->
    <item>
        <shape android:shape="rectangle">
            <stroke
                android:width="1dp"
                android:color="#60FFFFFF" />
            <corners android:radius="@dimen/service_card_corner_radius" />
        </shape>
    </item>

    <!-- Inner highlight -->
    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
        <shape android:shape="rectangle">
            <stroke
                android:width="0.5dp"
                android:color="#80FFFFFF" />
            <corners android:radius="@dimen/service_card_corner_radius" />
        </shape>
    </item>

</layer-list>
