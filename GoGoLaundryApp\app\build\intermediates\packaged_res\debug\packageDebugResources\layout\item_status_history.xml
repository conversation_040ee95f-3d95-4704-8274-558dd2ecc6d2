<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/status_indicator"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:background="@drawable/status_indicator_active"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/status_line"
        android:layout_width="2dp"
        android:layout_height="0dp"
        android:layout_marginStart="5dp"
        android:background="#C06363"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/status_indicator"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/status_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_medium"
        android:textColor="@color/info"
        android:textSize="@dimen/text_size_small"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@id/status_indicator"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Order Placed" />

    <TextView
        android:id="@+id/date_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_medium"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/text_size_micro"
        app:layout_constraintStart_toEndOf="@id/status_indicator"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/status_text"
        tools:text="May 22, 2025 at 02:54 PM" />

    <TextView
        android:id="@+id/notes_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_medium"
        android:layout_marginTop="@dimen/margin_extra_small"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/text_size_small"
        app:layout_constraintStart_toEndOf="@id/status_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/date_text"
        tools:text="Order created by customer" />

    <TextView
        android:id="@+id/updated_by_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_medium"
        android:layout_marginTop="@dimen/margin_extra_small"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/text_size_micro"
        android:textStyle="italic"
        app:layout_constraintStart_toEndOf="@id/status_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/notes_text"
        tools:text="Updated by System" />

</androidx.constraintlayout.widget.ConstraintLayout>
