<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Background circle with gradient -->
    <item>
        <shape android:shape="oval">
            <gradient
                android:startColor="#25FFFFFF"
                android:endColor="#15FFFFFF"
                android:angle="135" />
        </shape>
    </item>
    
    <!-- Glass effect overlay -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#20FFFFFF" />
        </shape>
    </item>
    
    <!-- Subtle border -->
    <item>
        <shape android:shape="oval">
            <stroke
                android:width="1.5dp"
                android:color="#30FFFFFF" />
        </shape>
    </item>
    
    <!-- Inner highlight -->
    <item android:insetLeft="2dp" android:insetTop="2dp" android:insetRight="2dp" android:insetBottom="2dp">
        <shape android:shape="oval">
            <stroke
                android:width="1dp"
                android:color="#40FFFFFF" />
        </shape>
    </item>
    
</layer-list>
