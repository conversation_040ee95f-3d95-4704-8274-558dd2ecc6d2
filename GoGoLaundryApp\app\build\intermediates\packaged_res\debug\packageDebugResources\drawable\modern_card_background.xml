<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Shadow effect for depth -->
    <item android:top="2dp" android:left="2dp">
        <shape android:shape="rectangle">
            <solid android:color="#10000000" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
    <!-- Main card background -->
    <item android:bottom="2dp" android:right="2dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
    <!-- Subtle border for definition -->
    <item android:bottom="2dp" android:right="2dp">
        <shape android:shape="rectangle">
            <stroke 
                android:width="0.5dp" 
                android:color="@color/divider" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
</layer-list>
