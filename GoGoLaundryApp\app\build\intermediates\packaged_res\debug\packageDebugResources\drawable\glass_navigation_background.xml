<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Main glass background -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#4DFFFFFF" />
            <corners 
                android:topLeftRadius="20dp"
                android:topRightRadius="20dp"
                android:bottomLeftRadius="0dp"
                android:bottomRightRadius="0dp" />
        </shape>
    </item>
    
    <!-- Border for glass effect -->
    <item>
        <shape android:shape="rectangle">
            <stroke 
                android:width="1dp" 
                android:color="#66FFFFFF" />
            <corners 
                android:topLeftRadius="20dp"
                android:topRightRadius="20dp"
                android:bottomLeftRadius="0dp"
                android:bottomRightRadius="0dp" />
        </shape>
    </item>
    
    <!-- Inner highlight -->
    <item android:top="1dp" android:left="1dp" android:right="1dp">
        <shape android:shape="rectangle">
            <stroke 
                android:width="0.5dp" 
                android:color="#80FFFFFF" />
            <corners 
                android:topLeftRadius="19dp"
                android:topRightRadius="19dp"
                android:bottomLeftRadius="0dp"
                android:bottomRightRadius="0dp" />
        </shape>
    </item>
    
</layer-list>
