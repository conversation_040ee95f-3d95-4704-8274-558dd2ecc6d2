<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="@dimen/card_corner_radius"
    app:cardElevation="@dimen/elevation_card">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/margin_medium">

        <TextView
            android:id="@+id/order_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_size_medium"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Order #12345" />

        <TextView
            android:id="@+id/order_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_extra_small"
            android:textSize="@dimen/text_size_small"
            android:textColor="@color/text_secondary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/order_id"
            tools:text="Order Date: 2023-06-15" />

        <TextView
            android:id="@+id/order_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_extra_small"
            android:textSize="@dimen/text_size_small"
            android:textColor="@color/success"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/order_date"
            tools:text="Status: Delivered" />

        <TextView
            android:id="@+id/order_total"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_size_medium"
            android:textColor="@color/primary"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Total: ৳450" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_track_order"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_medium"
            android:text="@string/track_order"
            android:textSize="@dimen/text_size_small"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/order_status"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_reorder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_small"
            android:layout_marginTop="@dimen/margin_medium"
            android:text="@string/reorder"
            android:textSize="@dimen/text_size_small"
            app:layout_constraintStart_toEndOf="@id/btn_track_order"
            app:layout_constraintTop_toBottomOf="@id/order_status"
            style="@style/Widget.MaterialComponents.Button.TextButton" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
