<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/notification_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    app:strokeColor="@color/card_stroke_light"
    app:strokeWidth="0.5dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Notification Icon -->
        <ImageView
            android:id="@+id/notification_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:contentDescription="@string/notifications"
            android:src="@drawable/ic_notification"
            android:background="@drawable/circle_background"
            android:backgroundTint="@color/primary_light"
            app:tint="@color/primary"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- Optional Notification Image -->
        <ImageView
            android:id="@+id/notification_image"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginStart="12dp"
            android:visibility="gone"
            android:scaleType="centerCrop"
            android:background="@drawable/rounded_corner_background"
            android:contentDescription="@string/notification_image"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@id/notification_icon"
            tools:visibility="visible"
            tools:src="@drawable/placeholder_image" />

        <!-- Notification Title -->
        <TextView
            android:id="@+id/notification_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="@color/text_primary"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
            app:layout_constraintTop_toTopOf="@id/notification_icon"
            app:layout_constraintStart_toEndOf="@id/notification_image"
            app:layout_constraintEnd_toStartOf="@id/notification_read_status"
            tools:text="Order Status Update" />

        <!-- Read/Unread Icon -->
        <ImageView
            android:id="@+id/notification_read_status"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:contentDescription="@string/mark_as_read"
            android:src="@drawable/ic_info"
            app:tint="@color/primary"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Message -->
        <TextView
            android:id="@+id/notification_message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:maxLines="3"
            android:ellipsize="end"
            android:lineSpacingExtra="2dp"
            android:textColor="@color/text_secondary"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            app:layout_constraintTop_toBottomOf="@id/notification_title"
            app:layout_constraintStart_toStartOf="@id/notification_title"
            app:layout_constraintEnd_toEndOf="@id/notification_title"
            tools:text="Your order #12345 is being processed and will be ready soon." />

        <!-- Time -->
        <TextView
            android:id="@+id/notification_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/primary"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
            app:layout_constraintTop_toBottomOf="@id/notification_message"
            app:layout_constraintStart_toStartOf="@id/notification_message"
            tools:text="2h ago" />

        <!-- Type Label -->
        <TextView
            android:id="@+id/notification_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:background="@drawable/price_background"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp"
            android:textColor="@color/info"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
            app:layout_constraintStart_toEndOf="@id/notification_time"
            app:layout_constraintBottom_toBottomOf="@id/notification_time"
            tools:text="Order" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
