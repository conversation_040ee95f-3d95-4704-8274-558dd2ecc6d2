<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Section -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/header_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/primary"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="24dp">

                <!-- Header Title with Icon -->
                <LinearLayout
                    android:id="@+id/title_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_notification"
                        app:tint="@color/white" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Notifications"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                        android:textColor="@color/white"
                        android:textStyle="bold"
                        android:letterSpacing="0.02" />

                    <!-- Refresh Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/refresh_button"
                        style="@style/Widget.MaterialComponents.Button.TextButton"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:minWidth="48dp"
                        android:minHeight="48dp"
                        android:padding="12dp"
                        android:text=""
                        android:contentDescription="Refresh notifications"
                        app:icon="@drawable/ic_refresh"
                        app:iconTint="@color/white"
                        app:iconSize="24dp"
                        app:iconGravity="textStart"
                        app:cornerRadius="24dp"
                        app:rippleColor="@color/white"
                        android:backgroundTint="@android:color/transparent" />

                </LinearLayout>

                <!-- Subtitle -->
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:alpha="0.9"
                    android:fontFamily="sans-serif"
                    android:letterSpacing="0.01"
                    android:text="Stay updated with your order status"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_small"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/title_container" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Notifications Content Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="2dp"
            app:strokeColor="@color/card_stroke_light"
            app:strokeWidth="0.5dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="20dp">

                <!-- Section Title -->
                <LinearLayout
                    android:id="@+id/notifications_content_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_notification"
                        app:tint="@color/primary" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Recent Notifications"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- Notifications List Container -->
                <FrameLayout
                    android:id="@+id/notifications_list_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:minHeight="300dp"
                    app:layout_constraintTop_toBottomOf="@id/notifications_content_header">

                    <!-- SwipeRefreshLayout -->
                    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                        android:id="@+id/swipe_refresh_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/notifications_recycler_view"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:nestedScrollingEnabled="true"
                            android:paddingStart="8dp"
                            android:paddingTop="8dp"
                            android:paddingEnd="8dp"
                            android:paddingBottom="8dp"
                            android:scrollbars="vertical"
                            android:fadeScrollbars="true"
                            android:scrollbarStyle="outsideOverlay"
                            android:visibility="visible"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            tools:listitem="@layout/item_notification" />

                    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

                    <!-- Loading Progress -->
                    <LinearLayout
                        android:id="@+id/progress_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="40dp"
                        android:visibility="gone">

                        <ProgressBar
                            android:id="@+id/progress_bar"
                            style="?android:attr/progressBarStyle"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:indeterminateTint="@color/primary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:text="Loading notifications..."
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                    <!-- Empty State -->
                    <LinearLayout
                        android:id="@+id/empty_view"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="40dp"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            android:layout_marginBottom="16dp"
                            android:alpha="0.7"
                            android:src="@drawable/ic_notification"
                            app:tint="@color/primary" />

                        <TextView
                            android:id="@+id/empty_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="No Notifications"
                            android:textAlignment="center"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                            android:textColor="@color/text_primary"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/empty_subtitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="You're all caught up! No new notifications at the moment."
                            android:textAlignment="center"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                            android:textColor="@color/text_secondary"
                            android:lineSpacingExtra="2dp" />

                    </LinearLayout>

                </FrameLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Hidden Switch for Compatibility -->
        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/show_read_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
