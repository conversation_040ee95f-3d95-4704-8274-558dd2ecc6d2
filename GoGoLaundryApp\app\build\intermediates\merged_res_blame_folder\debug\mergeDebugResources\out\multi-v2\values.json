{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-mergeDebugResources-52:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5a47ec4f36dba6d2801b3015f7956237\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2115,2286", "startColumns": "4,4", "startOffsets": "136610,149361", "endColumns": "67,166", "endOffsets": "136673,149523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\60861d183da333a5e3e50b62b1bd06fe\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2101", "startColumns": "4", "startOffsets": "135790", "endColumns": "42", "endOffsets": "135828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\25ec63c15b5d0d9d2f52abeb23cb0f1c\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2102", "startColumns": "4", "startOffsets": "135833", "endColumns": "42", "endOffsets": "135871"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values\\integers.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2174", "startColumns": "4", "startOffsets": "140561", "endColumns": "51", "endOffsets": "140608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\20446b1898b661f2716cf92ee44e5419\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "694,695,696,697,698,699,700,701,2278,2279,2280,2281,2282,2283,2284,2285,2287,2288,2289,2290,2291,2292,2293,2294,2295", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "35650,35740,35820,35910,36000,36080,36161,36241,148321,148426,148607,148732,148839,149019,149142,149258,149528,149716,149821,150002,150127,150302,150450,150513,150575", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "35735,35815,35905,35995,36075,36156,36236,36316,148421,148602,148727,148834,149014,149137,149253,149356,149711,149816,149997,150122,150297,150445,150508,150570,150649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f136748a3cc2b2230abdfc86c83d7814\\transformed\\firebase-messaging-23.4.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2357", "startColumns": "4", "startOffsets": "154729", "endColumns": "81", "endOffsets": "154806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e68673ff0a29529606855b32bb45cd3c\\transformed\\fragment-1.5.4\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2029,2065,2106", "startColumns": "4,4,4", "startOffsets": "132063,133929,136040", "endColumns": "56,64,63", "endOffsets": "132115,133989,136099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\631e8b8af57632c25e8bbc12d6e9c8f6\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2222", "startColumns": "4", "startOffsets": "143804", "endColumns": "82", "endOffsets": "143882"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2757,2770,2775,2779,6213,6216,6220,6224,6326,6333,6338,6342,6352,6356,6362,6370,6540,6551,6559,6618,6622,6626,6774,6779,6783,7883,7902,7918,7930,7936,7944,7952,7956,7962,7968,7973,7979,7987,7995", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "189290,190041,190321,190465,429617,429741,429891,430043,436904,437276,437534,437719,438275,438425,438785,439245,451875,452434,452891,456297,456516,456737,465991,466223,466465,546352,547867,549037,549796,550167,550674,551216,551412,551810,552200,552556,552921,553428,553955", "endLines": "2767,2774,2778,2786,6215,6219,6223,6231,6332,6337,6341,6351,6355,6361,6369,6375,6550,6558,6562,6621,6625,6629,6778,6782,6786,7901,7917,7929,7935,7943,7951,7955,7961,7967,7972,7978,7986,7994,8000", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "189868,190316,190460,190994,429736,429886,430038,430475,437271,437529,437714,438270,438420,438780,439240,439609,452429,452886,453037,456511,456732,456950,466218,466460,466701,547862,549032,549791,550162,550669,551211,551407,551805,552195,552551,552916,553423,553950,554378"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,41,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,1668,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,51,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,1715,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "649,650,655,656,657,660,663,664,667,668,669,670,682,683,684,685,690,691,692,693,742,743,744,745,746,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,770,773,774,775,776,777,778,779,780,781,782,783,784,785,786,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1060,1061,1064,1069,1070,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1093,1094,1095,1096,1097,1098,1099,1101,1102,1110,1111,1112,1113,1114,1120,1121,1123,1124,1125,1126,1127,1128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "32848,32889,33250,33295,33345,33566,33769,33809,33966,34011,34068,34122,35004,35054,35109,35155,35456,35502,35549,35601,39074,39116,39163,39211,39251,39670,39723,39772,39831,39880,39932,39987,40041,40093,40147,40200,40255,40309,40365,40419,40587,40767,40818,40870,40923,40976,41031,41084,41138,41196,41250,41307,41366,41424,41480,60682,60735,60795,60849,60906,60961,61015,61071,61127,61188,61243,61300,61342,61542,61590,61799,62136,62188,62459,62512,62567,62629,62693,62751,62811,62864,62918,62972,63030,63087,63137,63191,63545,63597,63651,63697,63745,63791,63838,63935,63986,64492,64543,64590,64642,64691,65023,65071,65168,65208,65253,65298,65343,65388", "endColumns": "40,45,44,49,53,50,39,44,44,56,53,57,49,54,45,51,45,46,51,48,41,46,47,39,45,52,48,58,48,51,54,53,51,53,52,54,53,55,53,55,44,50,51,52,52,54,52,53,57,53,56,58,57,55,38,52,59,53,56,54,53,55,55,60,54,56,41,46,47,61,48,51,45,52,54,61,63,57,59,52,53,53,57,56,49,53,57,51,53,45,47,45,46,41,50,55,50,46,51,48,53,47,41,39,44,44,44,44,44", "endOffsets": "32884,32930,33290,33340,33394,33612,33804,33849,34006,34063,34117,34175,35049,35104,35150,35202,35497,35544,35596,35645,39111,39158,39206,39246,39292,39718,39767,39826,39875,39927,39982,40036,40088,40142,40195,40250,40304,40360,40414,40470,40627,40813,40865,40918,40971,41026,41079,41133,41191,41245,41302,41361,41419,41475,41514,60730,60790,60844,60901,60956,61010,61066,61122,61183,61238,61295,61337,61384,61585,61647,61843,62183,62229,62507,62562,62624,62688,62746,62806,62859,62913,62967,63025,63082,63132,63186,63244,63592,63646,63692,63740,63786,63833,63875,63981,64037,64538,64585,64637,64686,64740,65066,65108,65203,65248,65293,65338,65383,65428"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b1a457fa5d970673514889d79cab362f\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "148,238,241,588,641,642,644,645,646,647,648,651,652,658,659,661,662,671,672,673,674,675,676,677,678,738,739,740,741,747,748,751,752,771,772,971,973,975,977,979,980,981,982,983,984,985,986,1058,1059,1062,1063,1065,1066,1067,1068,1073,1074,1089,1090,1091,1092,1105,1106,1107,1108,1117,1118,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1286,1287,1299,1300,1301,1302,1303,1304,1305,1980,1981,1982,1983,1984,1985,1986,1987,2025,2026,2027,2028,2033,2056,2057,2066,2098,2107,2108,2111,2112,2182,2183,2184,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2660,2768,2769,2787,2788,2789,2811,2819,2820,2824,2828,2839,2844,2873,2880,2884,2888,2893,2897,2901,2905,2909,2913,2917,2923,2927,2933,2937,2943,2947,2952,2956,2959,2963,2969,2973,2979,2983,2989,2992,2996,3000,3004,3008,3012,3013,3014,3015,3018,3021,3024,3027,3031,3032,3033,3034,3075,3078,3080,3082,3084,3089,3090,3094,3100,3104,3105,3107,3119,3120,3124,3130,3134,3229,3230,3234,3261,3265,3266,3270,5072,5244,5270,5441,5467,5498,5506,5512,5528,5550,5555,5560,5570,5579,5588,5592,5599,5618,5625,5626,5635,5638,5641,5645,5649,5653,5656,5657,5662,5667,5677,5682,5689,5695,5696,5699,5703,5708,5710,5712,5715,5718,5720,5724,5727,5734,5737,5740,5744,5746,5750,5752,5754,5756,5760,5768,5776,5788,5794,5803,5806,5817,5820,5821,5826,5827,6376,6445,6519,6520,6530,6539,6563,6565,6569,6572,6575,6578,6581,6584,6587,6590,6594,6597,6600,6603,6607,6610,6614,6787,6788,6789,6790,6791,6792,6793,6794,6795,6796,6797,6798,6799,6800,6801,6802,6803,6804,6805,6806,6807,6809,6811,6812,6813,6814,6815,6816,6817,6818,6820,6821,6823,6824,6826,6828,6829,6831,6832,6833,6834,6835,6836,6838,6839,6840,6841,6842,7127,7129,7131,7133,7134,7135,7136,7137,7138,7139,7140,7141,7142,7143,7144,7145,7147,7148,7149,7150,7151,7152,7153,7155,7159,7331,7332,7333,7334,7335,7336,7340,7341,7342,8001,8003,8005,8007,8009,8011,8012,8013,8014,8016,8018,8020,8021,8022,8023,8024,8025,8026,8027,8028,8029,8030,8031,8034,8035,8036,8037,8039,8041,8042,8044,8045,8047,8049,8051,8052,8053,8054,8055,8056,8057,8058,8059,8060,8061,8062,8064,8065,8066,8067,8069,8070,8071,8072,8073,8075,8077,8079,8081,8082,8083,8084,8085,8086,8087,8088,8089,8090,8091,8092,8093,8094,8095", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7276,11821,11974,30140,32323,32378,32502,32566,32636,32697,32772,32935,33012,33399,33484,33617,33693,34180,34257,34335,34441,34547,34626,34706,34763,38794,38868,38943,39008,39297,39357,39525,39597,40632,40699,55342,55459,55576,55693,55810,55869,55923,55977,56030,56084,56138,56192,61389,61463,61652,61725,61848,61919,61991,62063,62344,62401,63249,63322,63396,63470,64161,64233,64306,64376,64839,64899,65433,65502,65571,65641,65715,65791,65855,65932,66008,66085,66150,66219,66296,66371,66440,66508,66585,66651,66712,66809,66874,66943,67042,67113,67172,67230,67287,67346,67410,67481,67553,67625,67697,67769,67836,67904,67972,68031,68094,68158,68248,68339,68399,68465,68532,68598,68668,68732,68785,68852,68913,68980,69093,69151,69214,69279,69344,69419,69492,69564,69608,69655,69701,69750,69811,69872,69933,69995,70059,70123,70187,70252,70315,70375,70436,70502,70561,70621,70683,70754,70814,75601,75687,76302,76392,76479,76567,76649,76732,76822,129005,129057,129115,129160,129226,129290,129347,129404,131858,131915,131963,132012,132265,133514,133561,133994,135665,136104,136168,136358,136418,141107,141181,141251,141329,141383,141453,141538,141586,141632,141693,141756,141822,141886,141957,142020,142085,142149,142210,142271,142323,142396,142470,142539,142614,142688,142762,142903,183301,189873,189951,190999,191087,191183,192692,193274,193363,193610,193891,194557,194842,196651,197128,197350,197572,197848,198075,198305,198535,198765,198995,199222,199641,199867,200292,200522,200950,201169,201452,201660,201791,202018,202444,202669,203096,203317,203742,203862,204138,204439,204763,205054,205368,205505,205636,205741,205983,206150,206354,206562,206833,206945,207057,207162,209255,209469,209615,209755,209841,210189,210277,210523,210941,211190,211272,211370,212027,212127,212379,212803,213058,219824,219913,220150,222174,222416,222518,222771,358340,369021,370537,381232,382760,384517,385143,385563,386824,388089,388345,388581,389128,389622,390227,390425,391005,392373,392748,392866,393404,393561,393757,394030,394286,394456,394597,394661,395026,395393,396069,396333,396671,397024,397118,397304,397610,397872,397997,398124,398363,398574,398693,398886,399063,399518,399699,399821,400080,400193,400380,400482,400589,400718,400993,401501,401997,402874,403168,403738,403887,404619,404791,404875,405211,405303,439614,444845,450560,450622,451200,451784,453042,453155,453384,453544,453696,453867,454033,454202,454369,454532,454775,454945,455118,455289,455563,455762,455967,466706,466790,466886,466982,467080,467180,467282,467384,467486,467588,467690,467790,467886,467998,468127,468250,468381,468512,468610,468724,468818,468958,469092,469188,469300,469400,469516,469612,469724,469824,469964,470100,470264,470394,470552,470702,470843,470987,471122,471234,471384,471512,471640,471776,471908,472038,472168,472280,489938,490084,490228,490366,490432,490522,490598,490702,490792,490894,491002,491110,491210,491290,491382,491480,491590,491642,491720,491826,491918,492022,492132,492254,492417,505562,505642,505742,505832,505942,506032,506273,506367,506473,554383,554483,554595,554709,554825,554941,555035,555149,555261,555363,555483,555605,555687,555791,555911,556037,556135,556229,556317,556429,556545,556667,556779,556954,557070,557156,557248,557360,557484,557551,557677,557745,557873,558017,558145,558214,558309,558424,558537,558636,558745,558856,558967,559068,559173,559273,559403,559494,559617,559711,559823,559909,560013,560109,560197,560315,560419,560523,560649,560737,560845,560945,561035,561145,561229,561331,561415,561469,561533,561639,561725,561835,561919", "endLines": "148,238,241,588,641,642,644,645,646,647,648,651,652,658,659,661,662,671,672,673,674,675,676,677,678,738,739,740,741,747,748,751,752,771,772,971,973,975,977,979,980,981,982,983,984,985,986,1058,1059,1062,1063,1065,1066,1067,1068,1073,1074,1089,1090,1091,1092,1105,1106,1107,1108,1117,1118,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1286,1287,1299,1300,1301,1302,1303,1304,1305,1980,1981,1982,1983,1984,1985,1986,1987,2025,2026,2027,2028,2033,2056,2057,2066,2098,2107,2108,2111,2112,2182,2183,2184,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2660,2768,2769,2787,2788,2789,2818,2819,2823,2827,2831,2843,2849,2879,2883,2887,2892,2896,2900,2904,2908,2912,2916,2922,2926,2932,2936,2942,2946,2951,2955,2958,2962,2968,2972,2978,2982,2988,2991,2995,2999,3003,3007,3011,3012,3013,3014,3017,3020,3023,3026,3030,3031,3032,3033,3034,3077,3079,3081,3083,3088,3089,3093,3099,3103,3104,3106,3118,3119,3123,3129,3133,3134,3229,3233,3260,3264,3265,3269,3297,5243,5269,5440,5466,5497,5505,5511,5527,5549,5554,5559,5569,5578,5587,5591,5598,5617,5624,5625,5634,5637,5640,5644,5648,5652,5655,5656,5661,5666,5676,5681,5688,5694,5695,5698,5702,5707,5709,5711,5714,5717,5719,5723,5726,5733,5736,5739,5743,5745,5749,5751,5753,5755,5759,5767,5775,5787,5793,5802,5805,5816,5819,5820,5825,5826,5831,6444,6514,6519,6529,6538,6539,6564,6568,6571,6574,6577,6580,6583,6586,6589,6593,6596,6599,6602,6606,6609,6613,6617,6787,6788,6789,6790,6791,6792,6793,6794,6795,6796,6797,6798,6799,6800,6801,6802,6803,6804,6805,6806,6808,6810,6811,6812,6813,6814,6815,6816,6817,6819,6820,6822,6823,6825,6827,6828,6830,6831,6832,6833,6834,6835,6837,6838,6839,6840,6841,6842,7128,7130,7132,7133,7134,7135,7136,7137,7138,7139,7140,7141,7142,7143,7144,7146,7147,7148,7149,7150,7151,7152,7154,7158,7162,7331,7332,7333,7334,7335,7339,7340,7341,7342,8002,8004,8006,8008,8010,8011,8012,8013,8015,8017,8019,8020,8021,8022,8023,8024,8025,8026,8027,8028,8029,8030,8033,8034,8035,8036,8038,8040,8041,8043,8044,8046,8048,8050,8051,8052,8053,8054,8055,8056,8057,8058,8059,8060,8061,8063,8064,8065,8066,8068,8069,8070,8071,8072,8074,8076,8078,8080,8081,8082,8083,8084,8085,8086,8087,8088,8089,8090,8091,8092,8093,8094,8095", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "7326,11861,12018,30176,32373,32435,32561,32631,32692,32767,32843,33007,33085,33479,33561,33688,33764,34252,34330,34436,34542,34621,34701,34758,34816,38863,38938,39003,39069,39352,39413,39592,39665,40694,40762,55396,55513,55630,55747,55864,55918,55972,56025,56079,56133,56187,56241,61458,61537,61720,61794,61914,61986,62058,62131,62396,62454,63317,63391,63465,63540,64228,64301,64371,64442,64894,64955,65497,65566,65636,65710,65786,65850,65927,66003,66080,66145,66214,66291,66366,66435,66503,66580,66646,66707,66804,66869,66938,67037,67108,67167,67225,67282,67341,67405,67476,67548,67620,67692,67764,67831,67899,67967,68026,68089,68153,68243,68334,68394,68460,68527,68593,68663,68727,68780,68847,68908,68975,69088,69146,69209,69274,69339,69414,69487,69559,69603,69650,69696,69745,69806,69867,69928,69990,70054,70118,70182,70247,70310,70370,70431,70497,70556,70616,70678,70749,70809,70877,75682,75769,76387,76474,76562,76644,76727,76817,76908,129052,129110,129155,129221,129285,129342,129399,129453,131910,131958,132007,132058,132294,133556,133605,134035,135692,136163,136225,136413,136470,141176,141246,141324,141378,141448,141533,141581,141627,141688,141751,141817,141881,141952,142015,142080,142144,142205,142266,142318,142391,142465,142534,142609,142683,142757,142898,142968,183349,189946,190036,191082,191178,191268,193269,193358,193605,193886,194138,194837,195230,197123,197345,197567,197843,198070,198300,198530,198760,198990,199217,199636,199862,200287,200517,200945,201164,201447,201655,201786,202013,202439,202664,203091,203312,203737,203857,204133,204434,204758,205049,205363,205500,205631,205736,205978,206145,206349,206557,206828,206940,207052,207157,207274,209464,209610,209750,209836,210184,210272,210518,210936,211185,211267,211365,212022,212122,212374,212798,213053,213147,219908,220145,222169,222411,222513,222766,224922,369016,370532,381227,382755,384512,385138,385558,386819,388084,388340,388576,389123,389617,390222,390420,391000,392368,392743,392861,393399,393556,393752,394025,394281,394451,394592,394656,395021,395388,396064,396328,396666,397019,397113,397299,397605,397867,397992,398119,398358,398569,398688,398881,399058,399513,399694,399816,400075,400188,400375,400477,400584,400713,400988,401496,401992,402869,403163,403733,403882,404614,404786,404870,405206,405298,405576,444840,450211,450617,451195,451779,451870,453150,453379,453539,453691,453862,454028,454197,454364,454527,454770,454940,455113,455284,455558,455757,455962,456292,466785,466881,466977,467075,467175,467277,467379,467481,467583,467685,467785,467881,467993,468122,468245,468376,468507,468605,468719,468813,468953,469087,469183,469295,469395,469511,469607,469719,469819,469959,470095,470259,470389,470547,470697,470838,470982,471117,471229,471379,471507,471635,471771,471903,472033,472163,472275,472415,490079,490223,490361,490427,490517,490593,490697,490787,490889,490997,491105,491205,491285,491377,491475,491585,491637,491715,491821,491913,492017,492127,492249,492412,492569,505637,505737,505827,505937,506027,506268,506362,506468,506560,554478,554590,554704,554820,554936,555030,555144,555256,555358,555478,555600,555682,555786,555906,556032,556130,556224,556312,556424,556540,556662,556774,556949,557065,557151,557243,557355,557479,557546,557672,557740,557868,558012,558140,558209,558304,558419,558532,558631,558740,558851,558962,559063,559168,559268,559398,559489,559612,559706,559818,559904,560008,560104,560192,560310,560414,560518,560644,560732,560840,560940,561030,561140,561224,561326,561410,561464,561528,561634,561720,561830,561914,562034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e803a21e40f4f47a7be5edc22c63580a\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2105", "startColumns": "4", "startOffsets": "135990", "endColumns": "49", "endOffsets": "136035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\45823f9be481063fd3d75aa15b23a5cb\\transformed\\recyclerview-1.3.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "519,1295,1296,1297,1310,1311,1312,2035", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "26034,76096,76155,76203,77105,77180,77256,132353", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "26085,76150,76198,76254,77175,77251,77323,132414"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "3135,7185", "startColumns": "4,4", "startOffsets": "213152,493716", "endLines": "3152,7185", "endColumns": "12,69", "endOffsets": "214023,493781"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2210,2211,2212,2214,2215,2216,2217,2218,2219,2220,2221,2223,2224,2226,2227,2228,2238,2239,2250,2258,2259,2260,2261,2262,2263,2264,2265,2269,2270,2271,2273,2274,2275,2276,2277,2297,2298,2299,2300,2301,2302,2303,2304,2305,2307,2308,2309,2310,2312,2313,2314,2315,2316,2317,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2336,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2349,2353,2354,2355,2356,2359,2361,2362,2374,2375,2376,2377,2378,2384,2387,2388,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2437,2438,2444,2445,2468,2469,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2558,2559,2560,2561,2562,2563,2564,2565,2566,2568,2569,2570,2571,2572,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2607,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2634,2635,2638,2639,2640,2641,2642,2643,2644,2645,2646,2647,2648,2649,2650,2651,2652,2653,2654,2657,2658,2659,2661,2662,2668,2669,2670,2671,2672,2673,2674,2675,2676,2677,2678,2679,2680,2681,2682,2683,2684,2685,2686,2687,2688,2689,2690,2695,2696,2697,2698,2699,2700,2701,2704,2705,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2733,2734,2735,2736,2737,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2751,2752,2753,2754,2755,2756", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "143013,143076,143211,143373,143409,143461,143524,143574,143636,143688,143724,143887,143937,144138,144178,144231,145505,145616,146440,146989,147031,147131,147173,147227,147265,147326,147386,147795,147841,147944,148073,148113,148155,148209,148261,150698,150768,150830,150901,150978,151028,151079,151129,151191,151292,151340,151378,151442,151567,151629,151732,151794,151848,151916,152193,152243,152289,152337,152383,152429,152507,152553,152604,152675,152723,152831,153031,153176,153244,153318,153400,153468,153559,153641,153721,153795,153869,153945,154003,154464,154536,154594,154673,154871,154977,155038,155761,155803,155865,155913,155977,156545,156790,156828,157032,157099,157162,157231,157298,157362,157444,157521,157594,157685,157750,157835,157911,158020,158060,158149,158195,158283,158345,158425,158470,158544,158626,158677,158717,158790,158844,158902,158970,159012,160978,161037,161398,161460,164646,164712,175270,175318,175394,175468,175522,175576,175672,175765,175819,175857,175925,175983,176060,176122,176214,176274,176397,176469,176533,176591,176657,176711,176762,176826,176892,176966,177023,177078,177183,177217,177255,177315,177369,177429,177485,177539,177595,177644,177696,177744,177798,177852,177936,177992,178050,178109,178163,178219,178272,178330,178372,178453,178502,178538,178617,178671,178753,178812,178892,178954,179049,179116,179170,179216,179283,179377,179534,180330,180394,180458,180526,180634,180709,180809,180863,180940,181006,181107,181165,181217,181281,181334,181378,181418,181470,181520,181576,181619,181663,181754,181843,182092,182138,182197,182254,182301,182399,182443,182530,182572,182624,182668,182734,182784,182842,182891,182958,183012,183175,183221,183259,183354,183427,184020,184072,184142,184212,184272,184332,184425,184479,184547,184613,184679,184731,184791,184837,184882,184935,185014,185082,185142,185196,185250,185296,185349,185728,185774,185814,185868,185924,185972,186018,186424,186468,186678,186733,186788,186843,186901,186970,187025,187094,187143,187200,187247,187305,187366,187426,187493,187597,187639,187685,187735,187779,187821,187867,187905,187998,188038,188090,188138,188182,188308,188395,188441,188499,188560,188602,188666,188715,188772,188821,188867,188915,188976,189030,189078,189122,189177,189228", "endColumns": "62,134,61,35,51,62,49,61,51,35,79,49,50,39,52,37,110,37,37,41,99,41,53,37,60,59,110,45,102,49,39,41,53,51,59,69,61,70,76,49,50,49,61,53,47,37,63,55,61,102,61,53,67,118,49,45,47,45,45,77,45,50,70,47,107,39,79,67,73,81,67,90,81,79,73,73,75,57,57,71,57,78,55,41,60,65,41,61,47,63,43,51,37,49,66,62,68,66,63,81,76,72,90,64,84,75,49,39,88,45,87,61,79,44,73,81,50,39,72,53,57,67,41,48,58,97,61,53,65,41,47,75,73,53,53,95,92,53,37,67,57,76,61,91,59,122,71,63,57,65,53,50,63,65,73,56,54,55,33,37,59,53,59,55,53,55,48,51,47,53,53,83,55,57,58,53,55,52,57,41,80,48,35,78,53,81,58,79,61,94,66,53,45,66,93,78,71,63,63,67,107,74,99,53,76,65,100,57,51,63,52,43,39,51,49,55,42,43,90,88,108,45,58,56,46,97,43,86,41,51,43,65,49,57,48,66,53,55,45,37,41,72,84,51,69,69,59,59,92,53,67,65,65,51,59,45,44,52,78,67,59,53,53,45,52,92,45,39,53,55,47,45,57,43,50,54,54,54,57,68,54,68,48,56,46,57,60,59,66,103,41,45,49,43,41,45,37,47,39,51,47,43,81,86,45,57,60,41,63,48,56,48,45,47,60,53,47,43,54,50,61", "endOffsets": "143071,143206,143268,143404,143456,143519,143569,143631,143683,143719,143799,143932,143983,144173,144226,144264,145611,145649,146473,147026,147126,147168,147222,147260,147321,147381,147492,147836,147939,147989,148108,148150,148204,148256,148316,150763,150825,150896,150973,151023,151074,151124,151186,151240,151335,151373,151437,151493,151624,151727,151789,151843,151911,152030,152238,152284,152332,152378,152424,152502,152548,152599,152670,152718,152826,152866,153106,153239,153313,153395,153463,153554,153636,153716,153790,153864,153940,153998,154056,154531,154589,154668,154724,154908,155033,155099,155798,155860,155908,155972,156016,156592,156823,156873,157094,157157,157226,157293,157357,157439,157516,157589,157680,157745,157830,157906,157956,158055,158144,158190,158278,158340,158420,158465,158539,158621,158672,158712,158785,158839,158897,158965,159007,159056,161032,161130,161455,161509,164707,164749,175313,175389,175463,175517,175571,175667,175760,175814,175852,175920,175978,176055,176117,176209,176269,176392,176464,176528,176586,176652,176706,176757,176821,176887,176961,177018,177073,177129,177212,177250,177310,177364,177424,177480,177534,177590,177639,177691,177739,177793,177847,177931,177987,178045,178104,178158,178214,178267,178325,178367,178448,178497,178533,178612,178666,178748,178807,178887,178949,179044,179111,179165,179211,179278,179372,179451,179601,180389,180453,180521,180629,180704,180804,180858,180935,181001,181102,181160,181212,181276,181329,181373,181413,181465,181515,181571,181614,181658,181749,181838,181947,182133,182192,182249,182296,182394,182438,182525,182567,182619,182663,182729,182779,182837,182886,182953,183007,183063,183216,183254,183296,183422,183507,184067,184137,184207,184267,184327,184420,184474,184542,184608,184674,184726,184786,184832,184877,184930,185009,185077,185137,185191,185245,185291,185344,185437,185769,185809,185863,185919,185967,186013,186071,186463,186514,186728,186783,186838,186896,186965,187020,187089,187138,187195,187242,187300,187361,187421,187488,187592,187634,187680,187730,187774,187816,187862,187900,187948,188033,188085,188133,188177,188259,188390,188436,188494,188555,188597,188661,188710,188767,188816,188862,188910,188971,189025,189073,189117,189172,189223,189285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\4edb4fa237ded69772f2a19f3222541d\\transformed\\library-1.6.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "544,545,546,665,666,679,702,749,750,768,769,958,959,960,961,962,963,964,965,966,967,968,969,970,972,974,976,978,1033,1034,1071,1072,1100,1103,1104,1109,1115,1116,1119,1122,1210,1217,1224,1235,1958,2181,2318,2319,2320,10155,10161,10175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "27387,27449,27524,33854,33906,34821,36321,39418,39471,40475,40527,54612,54662,54719,54769,54826,54880,54941,54992,55050,55102,55160,55225,55284,55401,55518,55635,55752,59887,59935,62234,62285,63880,64042,64098,64447,64745,64791,64960,65113,70882,71244,71624,72343,127906,141060,152035,152084,152152,696064,696453,697268", "endLines": "544,545,546,665,666,679,702,749,750,768,769,958,959,960,961,962,963,964,965,966,967,968,969,970,972,974,976,978,1033,1034,1071,1072,1100,1103,1104,1109,1115,1116,1119,1122,1210,1217,1224,1235,1958,2181,2318,2319,2320,10160,10174,10186", "endColumns": "61,74,72,51,59,51,49,52,53,51,59,49,56,49,56,53,60,50,57,51,57,64,58,57,57,57,57,57,47,49,50,58,54,55,62,44,45,47,62,54,43,50,49,48,53,46,48,67,40,12,12,12", "endOffsets": "27444,27519,27592,33901,33961,34868,36366,39466,39520,40522,40582,54657,54714,54764,54821,54875,54936,54987,55045,55097,55155,55220,55279,55337,55454,55571,55688,55805,59930,59980,62280,62339,63930,64093,64156,64487,64786,64834,65018,65163,70921,71290,71669,72387,127955,141102,152079,152147,152188,696448,697263,697917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\6ae4eed1b1ae105f01d3e7330e2833b6\\transformed\\material-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "21,27,38,41,42,43,44,45,48,49,50,51,52,54,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,128,129,151,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,237,239,240,242,243,244,245,246,247,248,249,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,478,479,480,508,520,521,522,523,524,525,526,527,528,529,530,531,535,536,537,538,540,541,542,543,547,548,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,589,590,601,602,612,613,643,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1035,1036,1037,1038,1039,1040,1041,1042,1211,1223,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1988,1989,2038,2039,2040,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2059,2062,2063,2064,2082,2083,2084,2085,2086,2087,2088,2099,2109,2110,2113,2114,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2175,2177,2225,2232,2233,2234,2235,2236,2237,2266,2267,2268,2272,2333,2337,2350,2351,2352,2385,2389,2403,2421,2422,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2482,2485,2488,2489,2490,2491,2492,2493,2494,2495,2496,2497,2498,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2606,2608,2609,2610,2611,2663,2666,2667,2702,2703,2790,2794,2798,2802,2806,2807,2850,2858,2865,3035,3038,3048,3057,3066,3153,3154,3155,3156,3162,3163,3164,3165,3166,3167,3173,3174,3175,3176,3177,3182,3183,3187,3188,3194,3198,3199,3200,3201,3211,3212,3213,3217,3218,3224,3228,3298,3301,3302,3307,3308,3311,3312,3313,3314,3578,3585,3846,3852,4116,4123,4384,4390,4453,4535,4587,4669,4731,4813,4877,4929,5011,5019,5025,5036,5040,5044,5057,5832,5848,5855,5861,5878,5891,5911,5928,5937,5942,5949,5969,5982,5999,6005,6011,6018,6022,6028,6042,6045,6055,6056,6057,6105,6109,6113,6117,6118,6119,6122,6138,6145,6159,6204,6232,6238,6242,6246,6251,6258,6264,6265,6268,6272,6277,6290,6294,6299,6304,6309,6312,6315,6318,6322,6515,6516,6517,6518,6630,6631,6632,6633,6634,6635,6636,6637,6638,6639,6640,6641,6642,6643,6644,6645,6646,6647,6648,6652,6656,6660,6664,6668,6672,6676,6677,6678,6679,6680,6681,6682,6683,6687,6691,6692,6696,6697,6700,6704,6707,6710,6713,6717,6720,6723,6727,6731,6735,6739,6742,6743,6744,6745,6748,6752,6755,6758,6761,6764,6767,6770,6854,6857,6858,6861,6864,6865,6868,6869,6870,6874,6875,6880,6887,6894,6901,6908,6915,6922,6929,6936,6943,6952,6961,6970,6977,6986,6995,6998,7001,7002,7003,7004,7005,7006,7007,7008,7009,7010,7011,7012,7013,7017,7022,7027,7030,7031,7032,7033,7034,7042,7050,7051,7059,7063,7071,7079,7087,7095,7103,7104,7112,7120,7121,7124,7163,7165,7170,7172,7177,7181,7186,7187,7188,7189,7193,7197,7198,7202,7203,7204,7205,7206,7207,7208,7209,7210,7211,7212,7216,7217,7218,7219,7223,7224,7225,7226,7230,7234,7235,7239,7240,7241,7246,7247,7248,7249,7250,7251,7252,7253,7254,7255,7256,7257,7258,7259,7260,7261,7262,7263,7264,7265,7266,7270,7271,7272,7278,7279,7283,7285,7286,7291,7292,7293,7294,7295,7296,7300,7301,7302,7308,7309,7313,7315,7319,7323,7327,7343,7344,7345,7346,7349,7352,7355,7358,7361,7366,7370,7373,7374,7379,7383,7388,7394,7400,7405,7409,7414,7418,7422,7463,7464,7465,7466,7467,7471,7472,7473,7474,7478,7482,7486,7490,7494,7498,7502,7506,7512,7513,7554,7568,7573,7599,7606,7609,7620,7625,7628,7631,7686,7692,7693,7696,7699,7702,7705,7708,7711,7714,7718,7721,7722,7723,7731,7739,7742,7747,7752,7757,7762,7766,7770,7771,7779,7780,7781,7782,7783,7791,7796,7801,7802,7803,7804,7829,7835,7840,7843,7847,7850,7854,7864,7867,7872,7875,7879,8098,8106,8120,8133,8137,8152,8163,8166,8177,8182,8186,8221,8222,8223,8235,8243,8251,8259,8267,8287,8290,8317,8322,8342,8345,8348,8355,8368,8377,8380,8400,8410,8414,8418,8431,8435,8439,8443,8449,8453,8470,8478,8482,8486,8490,8493,8497,8501,8505,8515,8522,8529,8533,8559,8569,8594,8603,8623,8633,8637,8647,8672,8682,8685,8692,8699,8706,8707,8708,8709,8710,8717,8721,8727,8733,8734,8747,8748,8749,8752,8755,8758,8761,8764,8767,8770,8773,8776,8779,8782,8785,8788,8791,8794,8797,8800,8803,8806,8809,8812,8813,8821,8829,8830,8843,8853,8857,8862,8867,8871,8874,8878,8882,8885,8889,8892,8896,8901,8906,8909,8916,8920,8924,8933,8938,8943,8944,8948,8951,8955,8968,8973,8981,8985,8989,9006,9010,9015,9033,9040,9044,9074,9077,9080,9083,9086,9089,9092,9111,9117,9125,9132,9144,9152,9157,9165,9169,9187,9194,9210,9214,9222,9225,9230,9231,9232,9233,9237,9241,9245,9249,9284,9287,9291,9295,9329,9332,9336,9340,9349,9355,9358,9368,9372,9373,9380,9384,9391,9392,9393,9396,9401,9406,9407,9411,9426,9445,9449,9450,9462,9472,9473,9485,9490,9514,9517,9523,9526,9535,9543,9547,9550,9553,9556,9560,9563,9580,9584,9587,9602,9605,9613,9618,9625,9630,9631,9636,9637,9643,9649,9655,9687,9698,9715,9722,9726,9729,9742,9751,9755,9760,9764,9768,9772,9776,9780,9784,9788,9793,9796,9808,9813,9822,9825,9832,9833,9837,9846,9852,9856,9857,9861,9882,9888,9892,9896,9897,9915,9916,9917,9918,9919,9924,9927,9928,9934,9935,9947,9959,9966,9967,9972,9977,9978,9982,9996,10001,10007,10013,10019,10024,10030,10036,10037,10043,10058,10063,10072,10081,10084,10098,10103,10114,10118,10127,10136,10137,10144,14575,14576,14577,14578,14579,14580,14581,14582,14583,14584,14585,14586,14587,14588,14589,14590,14591,14592,14593,14594,14595,14596,14597,14598,14599,14600,14601,14602,14603,14604,14605,14606,14607,14608,14609,14610,14611,14612,14613,14614,14615,14616,14617,14618,14619,14620,14621,14622,14623,14624,14625,14626,14627,14628,14629,14630,14631,14632,14633,14634,14635,14636,14637,14638,14639,14640,14641,14642,14643,14644,14645,14646,14647,14648,14649,14650,14651,14652,14653,14654,14655,14656,14657,14658,14659,14660,14661,14662,14663,14664,14665,14666,14667,14668,14669,14670,14671,14672,14673,14674,14675,14676,14677,14678,14679,14680,14681,14682,14683,14684,14685,14686,14687,14688,14689,14690,14691,14692,14693,14694,14695,14696,14697,14698,14699,14700,14701,14702,14703,14704,14705,14706,14707,14708,14709,14710,14711,14712,14713,14714,14715,14716,14717,14718,14719,14720,14721,14722,14723,14724,14725,14726,14727,14728,14729,14730,14731,14732,14733,14734,14735,14736,14737,14738,14739,14740,14741,14742,14743,14744,14745,14746,14747,14748,14749,14750,14751,14752,14753,14754,14755,14756,14757,14758,14759,14760,14761,14762,14763,14764,14765,14766,14767,14768,14769,14770,14771,14772,14773,14774,14775,14776,14777,14778,14779,14780,14781,14782,14783,14784,14785,14786,14787,14788,14789,14790,14791,14792,14793,14794,14795,14796,14797,14798,14799,14800,14801,14802,14803,14804,14805,14806,14807,14808,14809,14810,14811,14812,14813,14814,14815,14816,14817,14818,14819,14820,14821,14822,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "825,1067,1520,1678,1734,1794,1855,1920,2075,2125,2175,2228,2286,2385,2755,2803,2874,2946,3018,3091,3158,3207,3261,3298,3349,3409,3456,3512,3561,3619,3673,3734,3790,3841,3901,3957,4020,4069,4125,4181,4231,4290,4345,4407,4454,4508,4564,4616,4671,4725,4779,4833,4882,4940,4994,5051,5107,5154,5207,5263,5323,5386,5445,5507,5557,5611,5665,5713,5770,5823,6447,6501,7432,7543,7605,7661,7721,7774,7835,7914,7995,8067,8146,8226,8302,8380,8449,8525,8602,8673,8746,8822,8900,8969,9045,9122,9186,9257,11770,11866,11919,12023,12090,12143,12195,12245,12303,12368,12416,19357,19424,19490,19548,19617,19675,19744,19814,19887,19961,20029,20096,20166,20232,20305,20365,20441,20501,20561,20636,20704,20770,20838,20898,20957,21014,21080,21142,21199,21267,21340,21410,21472,21533,21601,21663,21733,21802,21858,21917,21979,22041,22108,22165,22226,22287,22348,22409,22465,22521,22577,22633,22691,22749,22807,22865,22922,22979,23036,23093,23152,23211,23269,23352,23435,23508,23562,23631,23687,23768,23849,23920,24255,24308,24366,25531,26090,26136,26196,26250,26320,26390,26455,26521,26586,26654,26723,26791,26921,26974,27033,27091,27189,27239,27291,27337,27597,27653,27747,27805,27863,27925,27988,28050,28109,28169,28234,28300,28365,28427,28489,28551,28613,28675,28737,28803,28870,28936,28999,29063,29126,29194,29255,29317,29379,29442,29506,29569,29633,29711,29770,29836,29916,29977,30181,30239,30672,30717,31184,31248,32440,36371,36445,36516,36582,36656,36725,36796,36869,36940,37008,37081,37157,37227,37305,37373,37439,37500,37569,37633,37699,37767,37833,37896,37964,38035,38100,38173,38236,38317,38381,38447,38517,38587,38657,38727,41519,41576,41634,41693,41753,41812,41871,41930,41989,42048,42107,42166,42225,42284,42343,42403,42464,42526,42587,42648,42709,42770,42831,42892,42952,43013,43074,43134,43195,43256,43317,43378,43439,43500,43561,43622,43683,43744,43805,43873,43942,44012,44081,44150,44219,44288,44357,44426,44495,44564,44633,44702,44762,44823,44885,44946,45007,45068,45129,45190,45251,45312,45373,45434,45495,45557,45620,45684,45747,45810,45873,45936,45999,46062,46125,46188,46251,46314,46375,46437,46500,46562,46624,46686,46748,46810,46872,46934,46996,47058,47120,47177,47263,47343,47433,47528,47620,47712,47802,47885,47978,48065,48162,48253,48354,48441,48544,48633,48732,48824,48924,49008,49102,49190,49288,49371,49462,49556,49655,49757,49855,49955,50042,50142,50228,50324,50412,50493,50584,50680,50773,50866,50957,51042,51136,51225,51323,51416,51518,51606,51710,51801,51901,51994,52095,52180,52275,52364,52463,52548,52640,52735,52835,52938,53037,53140,53229,53330,53417,53514,53602,53698,53790,53890,53980,54078,54163,54252,54341,54434,54521,56246,56312,56388,56457,56536,56609,56689,56769,56846,56914,56992,57068,57139,57220,57293,57376,57451,57536,57609,57690,57771,57845,57929,57999,58077,58147,58227,58305,58377,58459,58529,58606,58686,58771,58859,58943,59030,59104,59182,59260,59331,59412,59503,59586,59682,59780,59985,60050,60116,60169,60245,60311,60398,60474,70926,71569,72501,72555,72634,72712,72785,72850,72913,72979,73050,73121,73191,73253,73322,73388,73448,73515,73582,73638,73689,73742,73794,73848,73919,73982,74041,74103,74162,74235,74302,74372,74432,74495,74570,74642,74738,74809,74865,74936,74993,75050,75116,75180,75251,75308,75361,75424,75476,75534,77328,77397,77463,77522,77605,77664,77721,77788,77858,77932,77994,78063,78133,78232,78329,78428,78514,78600,78681,78756,78845,78936,79020,79079,79125,79191,79248,79315,79372,79454,79519,79585,79708,79792,79913,79978,80040,80138,80212,80295,80384,80448,80527,80601,80663,80759,80824,80883,80939,80995,81055,81162,81209,81269,81330,81394,81455,81515,81573,81616,81665,81717,81768,81820,81869,81918,81983,82049,82109,82170,82226,82285,82334,82382,82440,82497,82599,82656,82731,82779,82830,82892,82957,83009,83083,83146,83209,83277,83327,83389,83449,83506,83566,83615,83683,83789,83891,83960,84031,84087,84136,84236,84307,84417,84508,84590,84688,84744,84845,84955,85054,85117,85223,85300,85412,85539,85651,85778,85848,85962,86093,86190,86258,86376,86479,86597,86658,86732,86799,86904,87026,87100,87167,87277,87376,87449,87546,87668,87786,87904,87965,88087,88204,88272,88378,88480,88560,88631,88727,88794,88868,88942,89028,89118,89196,89273,89373,89444,89565,89686,89750,89875,89949,90073,90197,90264,90373,90501,90613,90692,90770,90871,90942,91064,91186,91251,91377,91489,91595,91663,91762,91866,91929,91995,92079,92192,92305,92423,92501,92573,92709,92845,92930,93070,93208,93346,93488,93570,93656,93733,93806,93915,94026,94154,94282,94414,94544,94674,94808,94897,94959,95055,95122,95239,95360,95457,95539,95626,95713,95844,95975,96110,96187,96264,96375,96489,96563,96672,96784,96851,96924,96989,97091,97187,97291,97359,97424,97518,97590,97700,97806,97879,97970,98072,98175,98270,98377,98482,98604,98726,98852,98911,98969,99093,99217,99345,99463,99581,99703,99789,99886,100020,100154,100234,100372,100504,100636,100772,100847,100923,101026,101100,101213,101294,101351,101412,101471,101531,101589,101650,101708,101758,101807,101874,101933,101992,102041,102112,102196,102266,102337,102417,102486,102549,102617,102683,102751,102816,102882,102959,103037,103143,103249,103345,103474,103563,103690,103756,103826,103912,103978,104061,104135,104233,104329,104425,104523,104632,104727,104816,104878,104938,105003,105060,105141,105195,105252,105349,105459,105520,105635,105756,105851,105943,106036,106092,106151,106200,106292,106341,106395,106449,106503,106557,106611,106666,106776,106886,106994,107104,107214,107324,107434,107542,107648,107752,107856,107960,108055,108150,108243,108336,108440,108546,108650,108754,108847,108940,109033,109126,109234,109340,109446,109552,109649,109744,109839,109934,110040,110146,110252,110358,110456,110551,110647,110744,110809,110913,111294,111358,111419,111481,111541,111606,111668,111736,111794,111857,111920,111987,112062,112135,112201,112253,112306,112358,112415,112499,112594,112679,112760,112840,112917,112996,113073,113147,113221,113292,113372,113444,113519,113584,113645,113705,113780,113854,113927,113997,114069,114139,114212,114276,114346,114392,114461,114513,114598,114681,114738,114804,114871,114937,115018,115093,115149,115202,115263,115321,115371,115420,115469,115518,115580,115632,115677,115758,115809,115863,115916,115970,116021,116070,116136,116187,116248,116309,116371,116421,116462,116539,116598,116657,116716,116777,116833,116889,116956,117017,117082,117137,117202,117271,117339,117417,117486,117546,117617,117691,117756,117828,117898,117965,118049,118118,118185,118255,118318,118385,118453,118536,118615,118705,118782,118850,118917,118995,119052,119109,119177,119243,119299,119359,119418,119472,119522,119572,119620,119682,119733,119806,119886,119966,120030,120097,120168,120226,120287,120353,120412,120479,120539,120599,120662,120730,120791,120858,120936,121006,121055,121112,121181,121242,121330,121418,121506,121594,121681,121768,121855,121942,122000,122074,122144,122200,122271,122336,122398,122473,122546,122636,122702,122768,122829,122893,122955,123013,123084,123167,123226,123297,123363,123428,123489,123548,123619,123685,123750,123833,123909,123984,124065,124125,124194,124264,124333,124388,124444,124500,124561,124619,124675,124734,124788,124843,124905,124962,125056,125125,125226,125277,125347,125410,125466,125524,125583,125637,125723,125807,125877,125946,126016,126131,126252,126319,126386,126461,126528,126587,126641,126695,126749,126802,126854,129458,129595,132489,132538,132588,132679,132727,132783,132841,132903,132958,133016,133087,133151,133210,133272,133338,133652,133797,133841,133886,134864,134915,134962,135007,135058,135109,135160,135697,136230,136296,136475,136538,136678,136735,136789,136844,136902,136957,137016,137072,137141,137210,137279,137349,137412,137475,137538,137601,137666,137731,137796,137861,137924,137988,138052,138116,138167,138245,138323,138394,138466,138539,138611,138677,138743,138811,138879,138945,139012,139086,139149,139206,139266,139331,139398,139463,139520,139581,139639,139743,139853,139962,140066,140144,140209,140276,140342,140412,140459,140511,140613,140740,143988,144416,144547,144731,144909,145147,145336,147497,147595,147710,147994,152871,153111,154061,154150,154307,156597,156878,157961,159061,159248,159344,159434,159530,159620,159786,159909,160032,160202,160308,160423,160538,160640,160746,160863,161514,161596,161769,161937,162085,162244,162399,162572,162689,162806,162974,163086,163200,163372,163548,163706,163839,163951,164097,164249,164381,164524,164754,164932,165068,165164,165300,165395,165562,165655,165747,165934,166090,166268,166432,166614,166931,167113,167295,167485,167717,167907,168084,168246,168403,168513,168696,168833,169037,169221,169405,169565,169723,169907,170134,170337,170508,170728,170950,171105,171305,171489,171592,171782,171923,172088,172259,172459,172663,172865,173030,173235,173434,173633,173830,173921,174070,174220,174304,174453,174598,174750,174891,175057,179456,179606,179907,180073,180228,183512,183670,183834,186076,186299,191273,191550,191822,192100,192345,192407,195235,195686,196142,207279,207427,207941,208378,208812,214028,214113,214234,214333,214738,214835,214952,215039,215162,215263,215669,215768,215887,215980,216087,216430,216537,216782,216903,217312,217560,217660,217765,217884,218393,218540,218659,218910,219043,219458,219712,224927,225174,225299,225707,225828,226056,226177,226310,226457,247179,247671,268142,268566,289333,289827,310343,310769,315610,321027,325118,330549,335291,340668,344652,348644,354035,354582,355015,355771,356001,356244,357411,405581,406485,407069,407542,408972,409716,410909,411963,412441,412734,413117,414632,415397,416540,416981,417422,418018,418292,418703,419719,419897,420650,420787,420878,423072,423338,423660,423870,423979,424098,424282,425400,425870,426621,429204,430480,430856,431084,431340,431599,432175,432529,432651,432790,433082,433342,434270,434556,434959,435361,435704,435916,436117,436330,436619,450216,450289,450376,450461,456955,457067,457173,457296,457428,457551,457681,457805,457938,458069,458194,458311,458431,458563,458691,458805,458923,459036,459157,459345,459532,459713,459896,460080,460245,460427,460547,460667,460775,460885,460997,461105,461215,461380,461546,461698,461863,461964,462084,462255,462416,462579,462740,462907,463026,463143,463323,463505,463686,463869,464024,464169,464291,464426,464589,464782,464908,465060,465202,465372,465528,465700,473178,473373,473465,473638,473800,473895,474064,474158,474247,474490,474579,474872,475288,475708,476129,476555,476972,477388,477805,478223,478637,479107,479580,480052,480463,480934,481406,481596,481802,481908,482016,482122,482234,482348,482460,482574,482690,482804,482912,483022,483130,483392,483771,484175,484322,484430,484540,484648,484762,485171,485585,485701,486119,486360,486790,487225,487635,488057,488467,488589,488998,489414,489536,489754,492574,492642,492986,493066,493422,493572,493786,493862,493974,494064,494326,494591,494699,494851,494959,495035,495147,495237,495339,495447,495555,495655,495763,495848,496014,496118,496246,496333,496500,496578,496692,496784,497048,497315,497425,497578,497688,497772,498161,498259,498367,498461,498591,498699,498821,498957,499065,499185,499319,499441,499569,499711,499837,499977,500103,500221,500353,500451,500561,500861,500973,501091,501555,501671,501974,502100,502196,502597,502707,502831,502969,503079,503201,503513,503637,503767,504243,504371,504686,504824,504986,505202,505358,506565,506633,506717,506821,507024,507213,507414,507607,507812,508125,508337,508503,508619,508865,509081,509394,509820,510282,510519,510671,510931,511075,511217,514449,514563,514683,514799,514893,515214,515313,515431,515532,515811,516096,516375,516657,516910,517169,517422,517678,518102,518178,521428,522783,523227,525081,525656,525864,526874,527254,527420,527561,532581,533007,533119,533254,533407,533604,533775,533958,534133,534320,534592,534750,534834,534938,535425,535981,536139,536358,536589,536812,537047,537269,537535,537673,538272,538386,538524,538636,538760,539331,539826,540372,540517,540610,540702,542629,543199,543497,543686,543892,544085,544295,545179,545324,545716,545874,546091,562178,562610,563485,564105,564302,565250,566015,566138,566911,567132,567332,569309,569409,569499,570185,570938,571703,572466,573241,574454,574619,576232,576553,577616,577826,577996,578566,579461,580094,580260,581746,582362,582598,582819,583777,584042,584307,584554,584968,585204,586489,586938,587125,587374,587616,587792,588033,588266,588491,589086,589561,590085,590346,591697,592172,593398,593868,594916,595368,595612,596069,597314,597797,597947,598502,598954,599354,599507,599652,599795,599865,600293,600581,601085,601594,601710,602612,602734,602846,603023,603289,603559,603825,604093,604349,604609,604865,605123,605375,605631,605883,606137,606369,606605,606857,607113,607365,607619,607851,608085,608197,608849,609304,609428,610520,611335,611531,611855,612244,612596,612837,613051,613350,613542,613857,614064,614410,614710,615111,615330,615743,615980,616350,617074,617429,617698,617838,618092,618236,618513,619505,619914,620546,620892,621260,622334,622697,623097,624605,625190,625508,628043,628237,628455,628681,628893,629092,629299,630503,630798,631355,631745,632377,632854,633099,633586,633832,635028,635425,636431,636653,637076,637267,637646,637734,637842,637950,638263,638588,638907,639238,641941,642129,642390,642639,645223,645415,645680,645933,646465,646873,647072,647656,647891,648015,648427,648641,649043,649146,649276,649451,649703,649899,650039,650233,651244,652313,652601,652731,653508,654165,654311,655017,655255,656795,656945,657362,657527,658213,658683,658879,658970,659054,659198,659432,659599,660527,660813,660973,661588,661747,662075,662302,662814,663176,663255,663594,663699,664064,664435,664796,666670,667299,668375,668799,669052,669204,670252,670989,671192,671438,671685,671903,672145,672466,672730,673035,673258,673569,673758,674473,674742,675236,675462,675902,676061,676345,677090,677455,677760,677918,678156,679475,679873,680101,680321,680463,681753,681859,681989,682127,682251,682539,682708,682808,683093,683207,684090,684845,685284,685408,685654,685847,685981,686172,686951,687169,687460,687739,688056,688278,688573,688856,688960,689301,690117,690433,690994,691500,691705,692491,692896,693557,693746,694297,694863,694983,695385,837460,837555,837648,837711,837793,837886,837979,838066,838164,838255,838346,838434,838518,838614,838714,838820,838923,839024,839128,839234,839333,839439,839541,839648,839757,839868,839999,840119,840235,840353,840452,840559,840675,840794,840922,841011,841106,841183,841272,841363,841456,841530,841627,841722,841820,841919,842023,842119,842221,842324,842424,842527,842612,842713,842811,842901,842996,843083,843189,843291,843385,843476,843570,843646,843738,843827,843930,844041,844124,844210,844305,844402,844498,844586,844687,844788,844891,844997,845095,845192,845287,845385,845488,845588,845691,845796,845914,846030,846125,846218,846303,846399,846493,846585,846668,846772,846877,846977,847078,847183,847283,847384,847483,847585,847679,847786,847888,847991,848084,848180,848282,848385,848481,848583,848686,848783,848886,848984,849088,849193,849290,849398,849512,849627,849735,849849,849964,850066,850171,850279,850389,850505,850622,850717,850814,850913,851018,851124,851223,851328,851434,851534,851640,851741,851848,851967,852066,852171,852273,852375,852475,852578,852673,852777,852862,852966,853070,853168,853272,853378,853476,853581,853679,853792,853886,853975,854064,854147,854238,854321,854419,854509,854605,854694,854788,854876,854972,855057,855165,855266,855367,855465,855571,855662,855761,855858,855956,856052,856145,856255,856353,856448,856558,856650,856750,856849,856936,857040,857145,857244,857351,857458,857557,857666,857758,857869,857980,858091,858195,858310,858426,858553,858673,858768,858863,858960,859059,859151,859250,859342,859441,859527,859621,859724,859820,859923,860019,860122,860219,860317,860420,860513,860603,860704,860787,860878,860963,861055,861158,861253,861349,861442,861536,861615,861722,861813,861912,862005,862108,862212,862313,862414,862518,862612,862716,862820,862933,863039,863145,863253,863370,863471,863579,863679,863782,863887,863994,864090,864169,864259,864343,864435,864508,864600,864689,864781,864866,864963,865056,865151,865250,865347,865438,865529,865621,865716,865823,865931,866033,866130,866227,866320,866407,866491,866588,866685,866778,866865,866956,867055,867154,867249,867338,867419,867518,867622,867719,867824,867921,868005,868104,868208,868305,868410,868507,868605,868706,868812,868911,869018,869117,869216,869307,869396,869485,869567,869660,869751,869862,869963,870063,870175,870288,870386,870494,870588,870688,870777,870869,870980,871090,871185,871301,871427,871553,871672,871800,871925,872050,872168,872295,872404,872513,872626,872749,872872,872988,873113,873210,873318,873440,873556,873672,873781,873869,873970,874059,874160,874247,874335,874432,874524,874630,874730,874806", "endLines": "21,27,38,41,42,43,44,45,48,49,50,51,52,54,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,128,129,151,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,237,239,240,242,243,244,245,246,247,248,249,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,473,478,479,480,508,520,521,522,523,524,525,526,527,528,529,530,534,535,536,537,538,540,541,542,543,547,548,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,589,590,601,602,612,613,643,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1035,1036,1037,1038,1039,1040,1041,1042,1211,1223,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1988,1989,2038,2039,2040,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2059,2062,2063,2064,2082,2083,2084,2085,2086,2087,2088,2099,2109,2110,2113,2114,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2175,2180,2225,2232,2233,2234,2235,2236,2237,2266,2267,2268,2272,2335,2337,2350,2351,2352,2385,2389,2403,2421,2422,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2470,2471,2472,2473,2474,2475,2476,2477,2478,2481,2484,2487,2488,2489,2490,2491,2492,2493,2494,2495,2496,2497,2498,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2606,2608,2609,2610,2611,2665,2666,2667,2702,2703,2793,2797,2801,2805,2806,2810,2857,2864,2872,3037,3047,3056,3065,3074,3153,3154,3155,3161,3162,3163,3164,3165,3166,3172,3173,3174,3175,3176,3181,3182,3186,3187,3193,3197,3198,3199,3200,3210,3211,3212,3216,3217,3223,3227,3228,3300,3301,3306,3307,3310,3311,3312,3313,3577,3584,3845,3851,4115,4122,4383,4389,4452,4534,4586,4668,4730,4812,4876,4928,5010,5018,5024,5035,5039,5043,5056,5071,5847,5854,5860,5877,5890,5910,5927,5936,5941,5948,5968,5981,5998,6004,6010,6017,6021,6027,6041,6044,6054,6055,6056,6104,6108,6112,6116,6117,6118,6121,6137,6144,6158,6203,6204,6237,6241,6245,6250,6257,6263,6264,6267,6271,6276,6289,6293,6298,6303,6308,6311,6314,6317,6321,6325,6515,6516,6517,6518,6630,6631,6632,6633,6634,6635,6636,6637,6638,6639,6640,6641,6642,6643,6644,6645,6646,6647,6651,6655,6659,6663,6667,6671,6675,6676,6677,6678,6679,6680,6681,6682,6686,6690,6691,6695,6696,6699,6703,6706,6709,6712,6716,6719,6722,6726,6730,6734,6738,6741,6742,6743,6744,6747,6751,6754,6757,6760,6763,6766,6769,6773,6856,6857,6860,6863,6864,6867,6868,6869,6873,6874,6879,6886,6893,6900,6907,6914,6921,6928,6935,6942,6951,6960,6969,6976,6985,6994,6997,7000,7001,7002,7003,7004,7005,7006,7007,7008,7009,7010,7011,7012,7016,7021,7026,7029,7030,7031,7032,7033,7041,7049,7050,7058,7062,7070,7078,7086,7094,7102,7103,7111,7119,7120,7123,7126,7164,7169,7171,7176,7180,7184,7186,7187,7188,7192,7196,7197,7201,7202,7203,7204,7205,7206,7207,7208,7209,7210,7211,7215,7216,7217,7218,7222,7223,7224,7225,7229,7233,7234,7238,7239,7240,7245,7246,7247,7248,7249,7250,7251,7252,7253,7254,7255,7256,7257,7258,7259,7260,7261,7262,7263,7264,7265,7269,7270,7271,7277,7278,7282,7284,7285,7290,7291,7292,7293,7294,7295,7299,7300,7301,7307,7308,7312,7314,7318,7322,7326,7330,7343,7344,7345,7348,7351,7354,7357,7360,7365,7369,7372,7373,7378,7382,7387,7393,7399,7404,7408,7413,7417,7421,7462,7463,7464,7465,7466,7470,7471,7472,7473,7477,7481,7485,7489,7493,7497,7501,7505,7511,7512,7553,7567,7572,7598,7605,7608,7619,7624,7627,7630,7685,7691,7692,7695,7698,7701,7704,7707,7710,7713,7717,7720,7721,7722,7730,7738,7741,7746,7751,7756,7761,7765,7769,7770,7778,7779,7780,7781,7782,7790,7795,7800,7801,7802,7803,7828,7834,7839,7842,7846,7849,7853,7863,7866,7871,7874,7878,7882,8105,8119,8132,8136,8151,8162,8165,8176,8181,8185,8220,8221,8222,8234,8242,8250,8258,8266,8286,8289,8316,8321,8341,8344,8347,8354,8367,8376,8379,8399,8409,8413,8417,8430,8434,8438,8442,8448,8452,8469,8477,8481,8485,8489,8492,8496,8500,8504,8514,8521,8528,8532,8558,8568,8593,8602,8622,8632,8636,8646,8671,8681,8684,8691,8698,8705,8706,8707,8708,8709,8716,8720,8726,8732,8733,8746,8747,8748,8751,8754,8757,8760,8763,8766,8769,8772,8775,8778,8781,8784,8787,8790,8793,8796,8799,8802,8805,8808,8811,8812,8820,8828,8829,8842,8852,8856,8861,8866,8870,8873,8877,8881,8884,8888,8891,8895,8900,8905,8908,8915,8919,8923,8932,8937,8942,8943,8947,8950,8954,8967,8972,8980,8984,8988,9005,9009,9014,9032,9039,9043,9073,9076,9079,9082,9085,9088,9091,9110,9116,9124,9131,9143,9151,9156,9164,9168,9186,9193,9209,9213,9221,9224,9229,9230,9231,9232,9236,9240,9244,9248,9283,9286,9290,9294,9328,9331,9335,9339,9348,9354,9357,9367,9371,9372,9379,9383,9390,9391,9392,9395,9400,9405,9406,9410,9425,9444,9448,9449,9461,9471,9472,9484,9489,9513,9516,9522,9525,9534,9542,9546,9549,9552,9555,9559,9562,9579,9583,9586,9601,9604,9612,9617,9624,9629,9630,9635,9636,9642,9648,9654,9686,9697,9714,9721,9725,9728,9741,9750,9754,9759,9763,9767,9771,9775,9779,9783,9787,9792,9795,9807,9812,9821,9824,9831,9832,9836,9845,9851,9855,9856,9860,9881,9887,9891,9895,9896,9914,9915,9916,9917,9918,9923,9926,9927,9933,9934,9946,9958,9965,9966,9971,9976,9977,9981,9995,10000,10006,10012,10018,10023,10029,10035,10036,10042,10057,10062,10071,10080,10083,10097,10102,10113,10117,10126,10135,10136,10143,10151,14575,14576,14577,14578,14579,14580,14581,14582,14583,14584,14585,14586,14587,14588,14589,14590,14591,14592,14593,14594,14595,14596,14597,14598,14599,14600,14601,14602,14603,14604,14605,14606,14607,14608,14609,14610,14611,14612,14613,14614,14615,14616,14617,14618,14619,14620,14621,14622,14623,14624,14625,14626,14627,14628,14629,14630,14631,14632,14633,14634,14635,14636,14637,14638,14639,14640,14641,14642,14643,14644,14645,14646,14647,14648,14649,14650,14651,14652,14653,14654,14655,14656,14657,14658,14659,14660,14661,14662,14663,14664,14665,14666,14667,14668,14669,14670,14671,14672,14673,14674,14675,14676,14677,14678,14679,14680,14681,14682,14683,14684,14685,14686,14687,14688,14689,14690,14691,14692,14693,14694,14695,14696,14697,14698,14699,14700,14701,14702,14703,14704,14705,14706,14707,14708,14709,14710,14711,14712,14713,14714,14715,14716,14717,14718,14719,14720,14721,14722,14723,14724,14725,14726,14727,14728,14729,14730,14731,14732,14733,14734,14735,14736,14737,14738,14739,14740,14741,14742,14743,14744,14745,14746,14747,14748,14749,14750,14751,14752,14753,14754,14755,14756,14757,14758,14759,14760,14761,14762,14763,14764,14765,14766,14767,14768,14769,14770,14771,14772,14773,14774,14775,14776,14777,14778,14779,14780,14781,14782,14783,14784,14785,14786,14787,14788,14789,14790,14791,14792,14793,14794,14795,14796,14797,14798,14799,14800,14801,14802,14803,14804,14805,14806,14807,14808,14809,14810,14811,14812,14813,14814,14815,14816,14817,14818,14819,14820,14821,14822,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,60,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,85,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,82,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,85,76,72,108,110,127,127,131,129,129,133,88,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,66,72,64,101,95,103,67,64,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,69,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,69,85,65,82,73,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,94,95,96,64,103,57,63,60,61,59,64,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,58,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,203,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,10,103,127,86,10,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,152,144,142,69,10,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,92,86,97,90,90,87,83,95,99,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,88,94,76,88,90,92,73,96,94,97,98,103,95,101,102,99,102,84,100,97,89,94,86,105,101,93,90,93,75,91,88,102,110,82,85,94,96,95,87,100,100,102,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,82,103,104,99,100,104,99,100,98,101,93,106,101,102,92,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,94,96,98,104,105,98,104,105,99,105,100,106,118,98,104,101,101,99,102,94,103,84,103,103,97,103,105,97,104,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,94,94,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,92,89,100,82,90,84,91,102,94,95,92,93,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,100,107,99,102,104,106,95,78,89,83,91,72,91,88,91,84,96,92,94,98,96,90,90,91,94,106,107,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,103,96,104,96,83,98,103,96,104,96,97,100,105,98,106,98,98,90,88,88,81,92,90,110,100,99,111,112,97,107,93,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,121,115,115,108,87,100,88,100,86,87,96,91,105,99,75,76", "endOffsets": "876,1111,1570,1729,1789,1850,1915,1970,2120,2170,2223,2281,2329,2449,2798,2869,2941,3013,3086,3153,3202,3256,3293,3344,3404,3451,3507,3556,3614,3668,3729,3785,3836,3896,3952,4015,4064,4120,4176,4226,4285,4340,4402,4449,4503,4559,4611,4666,4720,4774,4828,4877,4935,4989,5046,5102,5149,5202,5258,5318,5381,5440,5502,5552,5606,5660,5708,5765,5818,5874,6496,6552,7490,7600,7656,7716,7769,7830,7909,7990,8062,8141,8221,8297,8375,8444,8520,8597,8668,8741,8817,8895,8964,9040,9117,9181,9252,9324,11816,11914,11969,12085,12138,12190,12240,12298,12363,12411,12462,19419,19485,19543,19612,19670,19739,19809,19882,19956,20024,20091,20161,20227,20300,20360,20436,20496,20556,20631,20699,20765,20833,20893,20952,21009,21075,21137,21194,21262,21335,21405,21467,21528,21596,21658,21728,21797,21853,21912,21974,22036,22103,22160,22221,22282,22343,22404,22460,22516,22572,22628,22686,22744,22802,22860,22917,22974,23031,23088,23147,23206,23264,23347,23430,23503,23557,23626,23682,23763,23844,23915,24044,24303,24361,24419,25584,26131,26191,26245,26315,26385,26450,26516,26581,26649,26718,26786,26916,26969,27028,27086,27138,27234,27286,27332,27382,27648,27695,27800,27858,27920,27983,28045,28104,28164,28229,28295,28360,28422,28484,28546,28608,28670,28732,28798,28865,28931,28994,29058,29121,29189,29250,29312,29374,29437,29501,29564,29628,29706,29765,29831,29911,29972,30025,30234,30285,30712,30773,31243,31302,32497,36440,36511,36577,36651,36720,36791,36864,36935,37003,37076,37152,37222,37300,37368,37434,37495,37564,37628,37694,37762,37828,37891,37959,38030,38095,38168,38231,38312,38376,38442,38512,38582,38652,38722,38789,41571,41629,41688,41748,41807,41866,41925,41984,42043,42102,42161,42220,42279,42338,42398,42459,42521,42582,42643,42704,42765,42826,42887,42947,43008,43069,43129,43190,43251,43312,43373,43434,43495,43556,43617,43678,43739,43800,43868,43937,44007,44076,44145,44214,44283,44352,44421,44490,44559,44628,44697,44757,44818,44880,44941,45002,45063,45124,45185,45246,45307,45368,45429,45490,45552,45615,45679,45742,45805,45868,45931,45994,46057,46120,46183,46246,46309,46370,46432,46495,46557,46619,46681,46743,46805,46867,46929,46991,47053,47115,47172,47258,47338,47428,47523,47615,47707,47797,47880,47973,48060,48157,48248,48349,48436,48539,48628,48727,48819,48919,49003,49097,49185,49283,49366,49457,49551,49650,49752,49850,49950,50037,50137,50223,50319,50407,50488,50579,50675,50768,50861,50952,51037,51131,51220,51318,51411,51513,51601,51705,51796,51896,51989,52090,52175,52270,52359,52458,52543,52635,52730,52830,52933,53032,53135,53224,53325,53412,53509,53597,53693,53785,53885,53975,54073,54158,54247,54336,54429,54516,54607,56307,56383,56452,56531,56604,56684,56764,56841,56909,56987,57063,57134,57215,57288,57371,57446,57531,57604,57685,57766,57840,57924,57994,58072,58142,58222,58300,58372,58454,58524,58601,58681,58766,58854,58938,59025,59099,59177,59255,59326,59407,59498,59581,59677,59775,59882,60045,60111,60164,60240,60306,60393,60469,60545,70986,71619,72550,72629,72707,72780,72845,72908,72974,73045,73116,73186,73248,73317,73383,73443,73510,73577,73633,73684,73737,73789,73843,73914,73977,74036,74098,74157,74230,74297,74367,74427,74490,74565,74637,74733,74804,74860,74931,74988,75045,75111,75175,75246,75303,75356,75419,75471,75529,75596,77392,77458,77517,77600,77659,77716,77783,77853,77927,77989,78058,78128,78227,78324,78423,78509,78595,78676,78751,78840,78931,79015,79074,79120,79186,79243,79310,79367,79449,79514,79580,79703,79787,79908,79973,80035,80133,80207,80290,80379,80443,80522,80596,80658,80754,80819,80878,80934,80990,81050,81157,81204,81264,81325,81389,81450,81510,81568,81611,81660,81712,81763,81815,81864,81913,81978,82044,82104,82165,82221,82280,82329,82377,82435,82492,82594,82651,82726,82774,82825,82887,82952,83004,83078,83141,83204,83272,83322,83384,83444,83501,83561,83610,83678,83784,83886,83955,84026,84082,84131,84231,84302,84412,84503,84585,84683,84739,84840,84950,85049,85112,85218,85295,85407,85534,85646,85773,85843,85957,86088,86185,86253,86371,86474,86592,86653,86727,86794,86899,87021,87095,87162,87272,87371,87444,87541,87663,87781,87899,87960,88082,88199,88267,88373,88475,88555,88626,88722,88789,88863,88937,89023,89113,89191,89268,89368,89439,89560,89681,89745,89870,89944,90068,90192,90259,90368,90496,90608,90687,90765,90866,90937,91059,91181,91246,91372,91484,91590,91658,91757,91861,91924,91990,92074,92187,92300,92418,92496,92568,92704,92840,92925,93065,93203,93341,93483,93565,93651,93728,93801,93910,94021,94149,94277,94409,94539,94669,94803,94892,94954,95050,95117,95234,95355,95452,95534,95621,95708,95839,95970,96105,96182,96259,96370,96484,96558,96667,96779,96846,96919,96984,97086,97182,97286,97354,97419,97513,97585,97695,97801,97874,97965,98067,98170,98265,98372,98477,98599,98721,98847,98906,98964,99088,99212,99340,99458,99576,99698,99784,99881,100015,100149,100229,100367,100499,100631,100767,100842,100918,101021,101095,101208,101289,101346,101407,101466,101526,101584,101645,101703,101753,101802,101869,101928,101987,102036,102107,102191,102261,102332,102412,102481,102544,102612,102678,102746,102811,102877,102954,103032,103138,103244,103340,103469,103558,103685,103751,103821,103907,103973,104056,104130,104228,104324,104420,104518,104627,104722,104811,104873,104933,104998,105055,105136,105190,105247,105344,105454,105515,105630,105751,105846,105938,106031,106087,106146,106195,106287,106336,106390,106444,106498,106552,106606,106661,106771,106881,106989,107099,107209,107319,107429,107537,107643,107747,107851,107955,108050,108145,108238,108331,108435,108541,108645,108749,108842,108935,109028,109121,109229,109335,109441,109547,109644,109739,109834,109929,110035,110141,110247,110353,110451,110546,110642,110739,110804,110908,110966,111353,111414,111476,111536,111601,111663,111731,111789,111852,111915,111982,112057,112130,112196,112248,112301,112353,112410,112494,112589,112674,112755,112835,112912,112991,113068,113142,113216,113287,113367,113439,113514,113579,113640,113700,113775,113849,113922,113992,114064,114134,114207,114271,114341,114387,114456,114508,114593,114676,114733,114799,114866,114932,115013,115088,115144,115197,115258,115316,115366,115415,115464,115513,115575,115627,115672,115753,115804,115858,115911,115965,116016,116065,116131,116182,116243,116304,116366,116416,116457,116534,116593,116652,116711,116772,116828,116884,116951,117012,117077,117132,117197,117266,117334,117412,117481,117541,117612,117686,117751,117823,117893,117960,118044,118113,118180,118250,118313,118380,118448,118531,118610,118700,118777,118845,118912,118990,119047,119104,119172,119238,119294,119354,119413,119467,119517,119567,119615,119677,119728,119801,119881,119961,120025,120092,120163,120221,120282,120348,120407,120474,120534,120594,120657,120725,120786,120853,120931,121001,121050,121107,121176,121237,121325,121413,121501,121589,121676,121763,121850,121937,121995,122069,122139,122195,122266,122331,122393,122468,122541,122631,122697,122763,122824,122888,122950,123008,123079,123162,123221,123292,123358,123423,123484,123543,123614,123680,123745,123828,123904,123979,124060,124120,124189,124259,124328,124383,124439,124495,124556,124614,124670,124729,124783,124838,124900,124957,125051,125120,125221,125272,125342,125405,125461,125519,125578,125632,125718,125802,125872,125941,126011,126126,126247,126314,126381,126456,126523,126582,126636,126690,126744,126797,126849,126923,129590,129730,132533,132583,132633,132722,132778,132836,132898,132953,133011,133082,133146,133205,133267,133333,133399,133690,133836,133881,133924,134910,134957,135002,135053,135104,135155,135206,135740,136291,136353,136533,136605,136730,136784,136839,136897,136952,137011,137067,137136,137205,137274,137344,137407,137470,137533,137596,137661,137726,137791,137856,137919,137983,138047,138111,138162,138240,138318,138389,138461,138534,138606,138672,138738,138806,138874,138940,139007,139081,139144,139201,139261,139326,139393,139458,139515,139576,139634,139738,139848,139957,140061,140139,140204,140271,140337,140407,140454,140506,140556,140665,141055,144133,144542,144726,144904,145142,145331,145500,147590,147705,147790,148068,153026,153171,154145,154302,154459,156745,157027,158015,159243,159339,159429,159525,159615,159781,159904,160027,160197,160303,160418,160533,160635,160741,160858,160973,161591,161764,161932,162080,162239,162394,162567,162684,162801,162969,163081,163195,163367,163543,163701,163834,163946,164092,164244,164376,164519,164641,164927,165063,165159,165295,165390,165557,165650,165742,165929,166085,166263,166427,166609,166926,167108,167290,167480,167712,167902,168079,168241,168398,168508,168691,168828,169032,169216,169400,169560,169718,169902,170129,170332,170503,170723,170945,171100,171300,171484,171587,171777,171918,172083,172254,172454,172658,172860,173025,173230,173429,173628,173825,173916,174065,174215,174299,174448,174593,174745,174886,175052,175213,179529,179902,180068,180223,180325,183665,183829,184015,186294,186419,191545,191817,192095,192340,192402,192687,195681,196137,196646,207422,207936,208373,208807,209250,214108,214229,214328,214733,214830,214947,215034,215157,215258,215664,215763,215882,215975,216082,216425,216532,216777,216898,217307,217555,217655,217760,217879,218388,218535,218654,218905,219038,219453,219707,219819,225169,225294,225702,225823,226051,226172,226305,226452,247174,247666,268137,268561,289328,289822,310338,310764,315605,321022,325113,330544,335286,340663,344647,348639,354030,354577,355010,355766,355996,356239,357406,358335,406480,407064,407537,408967,409711,410904,411958,412436,412729,413112,414627,415392,416535,416976,417417,418013,418287,418698,419714,419892,420645,420782,420873,423067,423333,423655,423865,423974,424093,424277,425395,425865,426616,429199,429294,430851,431079,431335,431594,432170,432524,432646,432785,433077,433337,434265,434551,434954,435356,435699,435911,436112,436325,436614,436899,450284,450371,450456,450555,457062,457168,457291,457423,457546,457676,457800,457933,458064,458189,458306,458426,458558,458686,458800,458918,459031,459152,459340,459527,459708,459891,460075,460240,460422,460542,460662,460770,460880,460992,461100,461210,461375,461541,461693,461858,461959,462079,462250,462411,462574,462735,462902,463021,463138,463318,463500,463681,463864,464019,464164,464286,464421,464584,464777,464903,465055,465197,465367,465523,465695,465986,473368,473460,473633,473795,473890,474059,474153,474242,474485,474574,474867,475283,475703,476124,476550,476967,477383,477800,478218,478632,479102,479575,480047,480458,480929,481401,481591,481797,481903,482011,482117,482229,482343,482455,482569,482685,482799,482907,483017,483125,483387,483766,484170,484317,484425,484535,484643,484757,485166,485580,485696,486114,486355,486785,487220,487630,488052,488462,488584,488993,489409,489531,489749,489933,492637,492981,493061,493417,493567,493711,493857,493969,494059,494321,494586,494694,494846,494954,495030,495142,495232,495334,495442,495550,495650,495758,495843,496009,496113,496241,496328,496495,496573,496687,496779,497043,497310,497420,497573,497683,497767,498156,498254,498362,498456,498586,498694,498816,498952,499060,499180,499314,499436,499564,499706,499832,499972,500098,500216,500348,500446,500556,500856,500968,501086,501550,501666,501969,502095,502191,502592,502702,502826,502964,503074,503196,503508,503632,503762,504238,504366,504681,504819,504981,505197,505353,505557,506628,506712,506816,507019,507208,507409,507602,507807,508120,508332,508498,508614,508860,509076,509389,509815,510277,510514,510666,510926,511070,511212,514444,514558,514678,514794,514888,515209,515308,515426,515527,515806,516091,516370,516652,516905,517164,517417,517673,518097,518173,521423,522778,523222,525076,525651,525859,526869,527249,527415,527556,532576,533002,533114,533249,533402,533599,533770,533953,534128,534315,534587,534745,534829,534933,535420,535976,536134,536353,536584,536807,537042,537264,537530,537668,538267,538381,538519,538631,538755,539326,539821,540367,540512,540605,540697,542624,543194,543492,543681,543887,544080,544290,545174,545319,545711,545869,546086,546347,562605,563480,564100,564297,565245,566010,566133,566906,567127,567327,569304,569404,569494,570180,570933,571698,572461,573236,574449,574614,576227,576548,577611,577821,577991,578561,579456,580089,580255,581741,582357,582593,582814,583772,584037,584302,584549,584963,585199,586484,586933,587120,587369,587611,587787,588028,588261,588486,589081,589556,590080,590341,591692,592167,593393,593863,594911,595363,595607,596064,597309,597792,597942,598497,598949,599349,599502,599647,599790,599860,600288,600576,601080,601589,601705,602607,602729,602841,603018,603284,603554,603820,604088,604344,604604,604860,605118,605370,605626,605878,606132,606364,606600,606852,607108,607360,607614,607846,608080,608192,608844,609299,609423,610515,611330,611526,611850,612239,612591,612832,613046,613345,613537,613852,614059,614405,614705,615106,615325,615738,615975,616345,617069,617424,617693,617833,618087,618231,618508,619500,619909,620541,620887,621255,622329,622692,623092,624600,625185,625503,628038,628232,628450,628676,628888,629087,629294,630498,630793,631350,631740,632372,632849,633094,633581,633827,635023,635420,636426,636648,637071,637262,637641,637729,637837,637945,638258,638583,638902,639233,641936,642124,642385,642634,645218,645410,645675,645928,646460,646868,647067,647651,647886,648010,648422,648636,649038,649141,649271,649446,649698,649894,650034,650228,651239,652308,652596,652726,653503,654160,654306,655012,655250,656790,656940,657357,657522,658208,658678,658874,658965,659049,659193,659427,659594,660522,660808,660968,661583,661742,662070,662297,662809,663171,663250,663589,663694,664059,664430,664791,666665,667294,668370,668794,669047,669199,670247,670984,671187,671433,671680,671898,672140,672461,672725,673030,673253,673564,673753,674468,674737,675231,675457,675897,676056,676340,677085,677450,677755,677913,678151,679470,679868,680096,680316,680458,681748,681854,681984,682122,682246,682534,682703,682803,683088,683202,684085,684840,685279,685403,685649,685842,685976,686167,686946,687164,687455,687734,688051,688273,688568,688851,688955,689296,690112,690428,690989,691495,691700,692486,692891,693552,693741,694292,694858,694978,695380,695914,837550,837643,837706,837788,837881,837974,838061,838159,838250,838341,838429,838513,838609,838709,838815,838918,839019,839123,839229,839328,839434,839536,839643,839752,839863,839994,840114,840230,840348,840447,840554,840670,840789,840917,841006,841101,841178,841267,841358,841451,841525,841622,841717,841815,841914,842018,842114,842216,842319,842419,842522,842607,842708,842806,842896,842991,843078,843184,843286,843380,843471,843565,843641,843733,843822,843925,844036,844119,844205,844300,844397,844493,844581,844682,844783,844886,844992,845090,845187,845282,845380,845483,845583,845686,845791,845909,846025,846120,846213,846298,846394,846488,846580,846663,846767,846872,846972,847073,847178,847278,847379,847478,847580,847674,847781,847883,847986,848079,848175,848277,848380,848476,848578,848681,848778,848881,848979,849083,849188,849285,849393,849507,849622,849730,849844,849959,850061,850166,850274,850384,850500,850617,850712,850809,850908,851013,851119,851218,851323,851429,851529,851635,851736,851843,851962,852061,852166,852268,852370,852470,852573,852668,852772,852857,852961,853065,853163,853267,853373,853471,853576,853674,853787,853881,853970,854059,854142,854233,854316,854414,854504,854600,854689,854783,854871,854967,855052,855160,855261,855362,855460,855566,855657,855756,855853,855951,856047,856140,856250,856348,856443,856553,856645,856745,856844,856931,857035,857140,857239,857346,857453,857552,857661,857753,857864,857975,858086,858190,858305,858421,858548,858668,858763,858858,858955,859054,859146,859245,859337,859436,859522,859616,859719,859815,859918,860014,860117,860214,860312,860415,860508,860598,860699,860782,860873,860958,861050,861153,861248,861344,861437,861531,861610,861717,861808,861907,862000,862103,862207,862308,862409,862513,862607,862711,862815,862928,863034,863140,863248,863365,863466,863574,863674,863777,863882,863989,864085,864164,864254,864338,864430,864503,864595,864684,864776,864861,864958,865051,865146,865245,865342,865433,865524,865616,865711,865818,865926,866028,866125,866222,866315,866402,866486,866583,866680,866773,866860,866951,867050,867149,867244,867333,867414,867513,867617,867714,867819,867916,868000,868099,868203,868300,868405,868502,868600,868701,868807,868906,869013,869112,869211,869302,869391,869480,869562,869655,869746,869857,869958,870058,870170,870283,870381,870489,870583,870683,870772,870864,870975,871085,871180,871296,871422,871548,871667,871795,871920,872045,872163,872290,872399,872508,872621,872744,872867,872983,873108,873205,873313,873435,873551,873667,873776,873864,873965,874054,874155,874242,874330,874427,874519,874625,874725,874801,874878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\bf2455e731a9e5bf1a1240e4ec32ba6d\\transformed\\constraintlayout-2.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "13,20,22,28,29,37,39,40,47,53,55,56,57,58,59,116,117,118,119,121,125,126,127,130,140,150,178,179,184,185,190,195,196,197,202,203,208,209,214,215,216,222,223,224,229,235,236,250,251,257,258,259,260,263,266,269,270,273,276,277,278,279,280,283,286,287,288,289,295,300,303,306,307,308,313,314,315,318,321,322,325,328,331,334,335,336,339,342,343,348,349,355,360,363,366,367,368,369,370,371,372,373,374,375,376,377,393,474,475,476,477,482,489,497,498,499,502,507,509,517,518,539,549,586,587,591,592,603,604,605,611,614,620,624,625,626,627,628,637,2041,2100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "485,769,881,1116,1177,1468,1575,1625,2027,2334,2454,2509,2569,2634,2693,5879,5931,5992,6054,6161,6294,6346,6396,6557,6964,7387,9329,9388,9585,9642,9837,10018,10072,10129,10321,10379,10575,10631,10825,10882,10933,11155,11207,11262,11452,11668,11718,12467,12523,12729,12790,12850,12920,13053,13184,13312,13380,13509,13635,13697,13760,13828,13895,14018,14143,14210,14275,14340,14629,14810,14931,15052,15118,15185,15395,15464,15530,15655,15781,15848,15974,16101,16226,16353,16409,16474,16600,16723,16788,16996,17063,17351,17531,17651,17771,17836,17898,17960,18024,18086,18145,18205,18266,18327,18386,18446,19106,24049,24100,24149,24197,24484,24776,25084,25131,25191,25297,25477,25589,25924,25978,27143,27700,30030,30081,30290,30342,30778,30837,30891,31129,31307,31509,31648,31694,31749,31794,31838,32186,132638,135745", "endLines": "19,20,26,28,36,37,39,40,47,53,55,56,57,58,59,116,117,118,119,124,125,126,127,139,147,150,178,183,184,189,194,195,196,201,202,207,208,213,214,215,221,222,223,228,234,235,236,250,256,257,258,259,262,265,268,269,272,275,276,277,278,279,282,285,286,287,288,294,299,302,305,306,307,312,313,314,317,320,321,324,327,330,333,334,335,338,341,342,347,348,354,359,362,365,366,367,368,369,370,371,372,373,374,375,376,392,398,474,475,476,477,488,496,497,498,501,506,507,516,517,518,539,549,586,587,591,600,603,604,610,611,619,623,624,625,626,627,636,640,2041,2100", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44", "endOffsets": "764,820,1062,1172,1463,1515,1620,1673,2070,2380,2504,2564,2629,2688,2750,5926,5987,6049,6095,6289,6341,6391,6442,6959,7271,7427,9383,9580,9637,9832,10013,10067,10124,10316,10374,10570,10626,10820,10877,10928,11150,11202,11257,11447,11663,11713,11765,12518,12724,12785,12845,12915,13048,13179,13307,13375,13504,13630,13692,13755,13823,13890,14013,14138,14205,14270,14335,14624,14805,14926,15047,15113,15180,15390,15459,15525,15650,15776,15843,15969,16096,16221,16348,16404,16469,16595,16718,16783,16991,17058,17346,17526,17646,17766,17831,17893,17955,18019,18081,18140,18200,18261,18322,18381,18441,19101,19352,24095,24144,24192,24250,24771,25079,25126,25186,25292,25472,25526,25919,25973,26029,27184,27742,30076,30135,30337,30667,30832,30886,31124,31179,31504,31643,31689,31744,31789,31833,32181,32318,132674,135785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\04c78b97304896bcd5afa736fff46d1e\\transformed\\osmdroid-android-6.1.17\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2209,2213,2229,2230,2231,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2296,2306,2358,2360,2363,2364,2365,2366,2367,2368,2369,2370,2371,2372,2373,2386,2439,2440,2441,2442,2443,2538,2567,2637,2655,2656,2691,2692,2693,2694,2706,2707,2732,2738", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "142973,143273,144269,144317,144378,145654,145745,145817,145904,145974,146049,146131,146233,146279,146392,150654,151245,154811,154913,155104,155159,155220,155276,155332,155397,155454,155516,155573,155630,155696,156750,161135,161183,161225,161288,161348,175218,177134,182030,183068,183131,185442,185514,185579,185642,186519,186565,187953,188264", "endColumns": "39,99,47,60,37,90,71,86,69,74,81,101,45,112,47,43,46,59,63,54,60,55,55,64,56,61,56,56,65,64,39,47,41,62,59,49,51,48,61,62,43,71,64,62,85,45,41,44,43", "endOffsets": "143008,143368,144312,144373,144411,145740,145812,145899,145969,146044,146126,146228,146274,146387,146435,150693,151287,154866,154972,155154,155215,155271,155327,155392,155449,155511,155568,155625,155691,155756,156785,161178,161220,161283,161343,161393,175265,177178,182087,183126,183170,185509,185574,185637,185723,186560,186602,187993,188303"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\0e546e96a375d1d88b2a082db553eacb\\transformed\\transition-1.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2030,2031,2054,2060,2061,2090,2091,2092,2093,2094,2095,2096,2097", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "132120,132160,133404,133695,133750,135246,135291,135345,135401,135453,135505,135554,135615", "endColumns": "39,46,42,54,46,44,53,55,51,51,48,60,49", "endOffsets": "132155,132202,133442,133745,133792,135286,135340,135396,135448,135500,135549,135610,135660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\0f716dfc83cb8e5033ee3eacd43859f9\\transformed\\glide-4.16.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2032", "startColumns": "4", "startOffsets": "132207", "endColumns": "57", "endOffsets": "132260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\98b62f2f5bc1112d0086d39f0eb418b1\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "481,653,654,680,681,1043,1044,1228,1229,1230,1231,1232,1233,1234,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2036,2037,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2089,2176,2251,2252,2253,2254,2255,2256,2257,2708,6843,6844,6848,6849,6853,8096,8097", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24424,33090,33162,34873,34938,60550,60619,71855,71925,71993,72065,72135,72196,72270,126928,126989,127050,127112,127176,127238,127299,127367,127467,127527,127593,127666,127735,127792,127844,129735,129807,129883,129948,130007,130066,130126,130186,130246,130306,130366,130426,130486,130546,130606,130666,130725,130785,130845,130905,130965,131025,131085,131145,131205,131265,131325,131384,131444,131504,131563,131622,131681,131740,131799,132419,132454,134040,134095,134158,134213,134271,134329,134390,134453,134510,134561,134611,134672,134729,134795,134829,135211,140670,146478,146545,146617,146686,146755,146829,146901,186607,472420,472537,472738,472848,473049,562039,562111", "endLines": "481,653,654,680,681,1043,1044,1228,1229,1230,1231,1232,1233,1234,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2036,2037,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2089,2176,2251,2252,2253,2254,2255,2256,2257,2708,6843,6847,6848,6852,6853,8096,8097", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "24479,33157,33245,34933,34999,60614,60677,71920,71988,72060,72130,72191,72265,72338,126984,127045,127107,127171,127233,127294,127362,127462,127522,127588,127661,127730,127787,127839,127901,129802,129878,129943,130002,130061,130121,130181,130241,130301,130361,130421,130481,130541,130601,130661,130720,130780,130840,130900,130960,131020,131080,131140,131200,131260,131320,131379,131439,131499,131558,131617,131676,131735,131794,131853,132449,132484,134090,134153,134208,134266,134324,134385,134448,134505,134556,134606,134667,134724,134790,134824,134859,135241,140735,146540,146612,146681,146750,146824,146896,146984,186673,472532,472733,472843,473044,473173,562106,562173"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2379,2380,2381,2382,2383,2636", "startColumns": "4,4,4,4,4,4", "startOffsets": "156021,156103,156207,156316,156436,181952", "endColumns": "81,103,108,119,108,77", "endOffsets": "156098,156202,156311,156431,156540,182025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\cfe645f5abe7e16c9eebab287fc0d954\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "120,10152", "startColumns": "4,4", "startOffsets": "6100,695919", "endLines": "120,10154", "endColumns": "60,12", "endOffsets": "6156,696059"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1212,1213,1214,1215,1216,1218,1219,1225,1226,1227,1236,1288,1289,1290,1291,1292,1293,1294,1298,1306,1307,1308,1309,1701,1702,1703,1704,1705,1706,1707,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "70991,71034,71091,71148,71199,71295,71344,71674,71734,71795,72392,75774,75819,75866,75911,75962,76010,76054,76259,76913,76960,77008,77055,110971,111020,111070,111119,111162,111206,111251,127960,128018,128071,128127,128183,128242,128291,128338,128385,128440,128482,128523,128576,128626,128672,128719,128767,128814,128861,128908,128959", "endColumns": "42,56,56,50,44,48,54,59,60,59,56,44,46,44,50,47,43,41,42,46,47,46,49,48,49,48,42,43,44,42,57,52,55,55,58,48,46,46,54,41,40,52,49,45,46,47,46,46,46,50,45", "endOffsets": "71029,71086,71143,71194,71239,71339,71394,71729,71790,71850,72444,75814,75861,75906,75957,76005,76049,76091,76297,76955,77003,77050,77100,111015,111065,111114,111157,111201,111246,111289,128013,128066,128122,128178,128237,128286,128333,128380,128435,128477,128518,128571,128621,128667,128714,128762,128809,128856,128903,128954,129000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d33f8fcb8e6ec6de69937c85231a8678\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "46,686,687,688,689,1220,1221,1222,2832,6205,6207,6210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1975,35207,35268,35330,35392,71399,71458,71515,194143,429299,429363,429489", "endLines": "46,686,687,688,689,1220,1221,1222,2838,6206,6209,6212", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "2022,35263,35325,35387,35451,71453,71510,71564,194552,429358,429484,429612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\00596551c31d248811f1b66873b9f7d4\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "149,152,1237", "startColumns": "4,4,4", "startOffsets": "7331,7495,72449", "endColumns": "55,47,51", "endOffsets": "7382,7538,72496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\6e9bc98679171a20e588e1fb7c8cf1c2\\transformed\\materialish-progress-1.7\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2311", "startColumns": "4", "startOffsets": "151498", "endColumns": "68", "endOffsets": "151562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ffc1f2a80960a680f82f6ab424275895\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2034,2055", "startColumns": "4,4", "startOffsets": "132299,133447", "endColumns": "53,66", "endOffsets": "132348,133509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\be59f2a5b3dd115371e7b6a1346b6da9\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2104", "startColumns": "4", "startOffsets": "135936", "endColumns": "53", "endOffsets": "135985"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values\\arrays.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2,7", "startColumns": "4,4", "startOffsets": "150,303", "endLines": "6,12", "endColumns": "19,19", "endOffsets": "298,480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c31bb31d0c0a9ca9efeb309970d98416\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2058,2103", "startColumns": "4,4", "startOffsets": "133610,135876", "endColumns": "41,59", "endOffsets": "133647,135931"}}]}, {"outputFile": "com.mdsadrulhasan.gogolaundry.app-mergeDebugResources-52:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5a47ec4f36dba6d2801b3015f7956237\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2127,2298", "startColumns": "4,4", "startOffsets": "137206,149957", "endColumns": "67,166", "endOffsets": "137269,150119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\60861d183da333a5e3e50b62b1bd06fe\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2113", "startColumns": "4", "startOffsets": "136386", "endColumns": "42", "endOffsets": "136424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\25ec63c15b5d0d9d2f52abeb23cb0f1c\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2114", "startColumns": "4", "startOffsets": "136429", "endColumns": "42", "endOffsets": "136467"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values\\integers.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2186", "startColumns": "4", "startOffsets": "141157", "endColumns": "51", "endOffsets": "141204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\20446b1898b661f2716cf92ee44e5419\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "695,696,697,698,699,700,701,702,2290,2291,2292,2293,2294,2295,2296,2297,2299,2300,2301,2302,2303,2304,2305,2306,2307", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "35702,35792,35872,35962,36052,36132,36213,36293,148917,149022,149203,149328,149435,149615,149738,149854,150124,150312,150417,150598,150723,150898,151046,151109,151171", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "35787,35867,35957,36047,36127,36208,36288,36368,149017,149198,149323,149430,149610,149733,149849,149952,150307,150412,150593,150718,150893,151041,151104,151166,151245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f136748a3cc2b2230abdfc86c83d7814\\transformed\\firebase-messaging-23.4.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2369", "startColumns": "4", "startOffsets": "155325", "endColumns": "81", "endOffsets": "155402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e68673ff0a29529606855b32bb45cd3c\\transformed\\fragment-1.5.4\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2041,2077,2118", "startColumns": "4,4,4", "startOffsets": "132659,134525,136636", "endColumns": "56,64,63", "endOffsets": "132711,134585,136695"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\631e8b8af57632c25e8bbc12d6e9c8f6\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2234", "startColumns": "4", "startOffsets": "144400", "endColumns": "82", "endOffsets": "144478"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2769,2782,2787,2791,6225,6228,6232,6236,6338,6345,6350,6354,6364,6368,6374,6382,6552,6563,6571,6630,6634,6638,6786,6791,6795,7895,7914,7930,7942,7948,7956,7964,7968,7974,7980,7985,7991,7999,8007", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "189886,190637,190917,191061,430213,430337,430487,430639,437500,437872,438130,438315,438871,439021,439381,439841,452471,453030,453487,456893,457112,457333,466587,466819,467061,546948,548463,549633,550392,550763,551270,551812,552008,552406,552796,553152,553517,554024,554551", "endLines": "2779,2786,2790,2798,6227,6231,6235,6243,6344,6349,6353,6363,6367,6373,6381,6387,6562,6570,6574,6633,6637,6641,6790,6794,6798,7913,7929,7941,7947,7955,7963,7967,7973,7979,7984,7990,7998,8006,8012", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "190464,190912,191056,191590,430332,430482,430634,431071,437867,438125,438310,438866,439016,439376,439836,440205,453025,453482,453633,457107,457328,457546,466814,467056,467297,548458,549628,550387,550758,551265,551807,552003,552401,552791,553147,553512,554019,554546,554974"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,150,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,157,-1,153,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,156,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,166,167,165,-1,-1,-1,-1,-1,160,161,162,-1,-1,152,-1,-1,-1,-1,-1,151,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,-1,-1,-1,-1,-1,4,4,4,-1,-1,4,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6607,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6916,-1,6757,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6864,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,7248,7298,7198,-1,-1,-1,-1,-1,7014,7058,7102,-1,-1,6707,-1,-1,-1,-1,-1,6659,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,51,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,54,-1,58,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,51,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,49,47,49,-1,-1,-1,-1,-1,43,43,43,-1,-1,49,-1,-1,-1,-1,-1,47,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6654,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6966,-1,6811,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6911,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,7293,7341,7243,-1,-1,-1,-1,-1,7053,7097,7141,-1,-1,6752,-1,-1,-1,-1,-1,6702,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "649,650,655,656,657,660,663,664,667,668,669,670,682,683,684,685,686,691,692,693,694,743,744,745,746,747,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,771,774,775,776,777,778,779,780,781,782,783,784,785,786,787,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1064,1065,1068,1073,1074,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1108,1109,1110,1111,1112,1113,1121,1122,1123,1124,1125,1126,1132,1133,1135,1136,1137,1138,1139,1140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "32848,32889,33250,33295,33345,33566,33769,33809,33966,34011,34068,34122,35004,35054,35109,35155,35207,35508,35554,35601,35653,39126,39168,39215,39263,39303,39722,39775,39824,39883,39932,39984,40039,40093,40145,40199,40252,40307,40361,40417,40471,40639,40819,40870,40922,40975,41028,41083,41136,41190,41248,41302,41359,41418,41476,41532,60734,60789,60842,60901,60961,61015,61072,61127,61181,61237,61293,61354,61409,61466,61508,61560,61760,61808,62017,62354,62406,62677,62730,62785,62847,62911,62969,63029,63082,63136,63190,63248,63305,63355,63409,63763,63815,63869,63919,63967,64017,64063,64111,64157,64204,64301,64345,64389,64433,64484,64540,65040,65091,65138,65190,65239,65293,65619,65667,65764,65804,65849,65894,65939,65984", "endColumns": "40,45,44,49,53,50,39,44,44,56,53,57,49,54,45,51,51,45,46,51,48,41,46,47,39,45,52,48,58,48,51,54,53,51,53,52,54,53,55,53,55,44,50,51,52,52,54,52,53,57,53,56,58,57,55,38,54,52,58,59,53,56,54,53,55,55,60,54,56,41,51,46,47,61,48,51,45,52,54,61,63,57,59,52,53,53,57,56,49,53,57,51,53,49,47,49,45,47,45,46,41,43,43,43,50,55,49,50,46,51,48,53,47,47,41,39,44,44,44,44,44", "endOffsets": "32884,32930,33290,33340,33394,33612,33804,33849,34006,34063,34117,34175,35049,35104,35150,35202,35254,35549,35596,35648,35697,39163,39210,39258,39298,39344,39770,39819,39878,39927,39979,40034,40088,40140,40194,40247,40302,40356,40412,40466,40522,40679,40865,40917,40970,41023,41078,41131,41185,41243,41297,41354,41413,41471,41527,41566,60784,60837,60896,60956,61010,61067,61122,61176,61232,61288,61349,61404,61461,61503,61555,61602,61803,61865,62061,62401,62447,62725,62780,62842,62906,62964,63024,63077,63131,63185,63243,63300,63350,63404,63462,63810,63864,63914,63962,64012,64058,64106,64152,64199,64241,64340,64384,64428,64479,64535,64585,65086,65133,65185,65234,65288,65336,65662,65704,65799,65844,65889,65934,65979,66024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b1a457fa5d970673514889d79cab362f\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "148,238,241,588,641,642,644,645,646,647,648,651,652,658,659,661,662,671,672,673,674,675,676,677,678,739,740,741,742,748,749,752,753,772,773,972,974,976,978,980,981,982,983,984,985,986,987,1062,1063,1066,1067,1069,1070,1071,1072,1077,1078,1093,1094,1095,1096,1116,1117,1118,1119,1129,1130,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1298,1299,1311,1312,1313,1314,1315,1316,1317,1992,1993,1994,1995,1996,1997,1998,1999,2037,2038,2039,2040,2045,2068,2069,2078,2110,2119,2120,2123,2124,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2672,2780,2781,2799,2800,2801,2823,2831,2832,2836,2840,2851,2856,2885,2892,2896,2900,2905,2909,2913,2917,2921,2925,2929,2935,2939,2945,2949,2955,2959,2964,2968,2971,2975,2981,2985,2991,2995,3001,3004,3008,3012,3016,3020,3024,3025,3026,3027,3030,3033,3036,3039,3043,3044,3045,3046,3087,3090,3092,3094,3096,3101,3102,3106,3112,3116,3117,3119,3131,3132,3136,3142,3146,3241,3242,3246,3273,3277,3278,3282,5084,5256,5282,5453,5479,5510,5518,5524,5540,5562,5567,5572,5582,5591,5600,5604,5611,5630,5637,5638,5647,5650,5653,5657,5661,5665,5668,5669,5674,5679,5689,5694,5701,5707,5708,5711,5715,5720,5722,5724,5727,5730,5732,5736,5739,5746,5749,5752,5756,5758,5762,5764,5766,5768,5772,5780,5788,5800,5806,5815,5818,5829,5832,5833,5838,5839,6388,6457,6531,6532,6542,6551,6575,6577,6581,6584,6587,6590,6593,6596,6599,6602,6606,6609,6612,6615,6619,6622,6626,6799,6800,6801,6802,6803,6804,6805,6806,6807,6808,6809,6810,6811,6812,6813,6814,6815,6816,6817,6818,6819,6821,6823,6824,6825,6826,6827,6828,6829,6830,6832,6833,6835,6836,6838,6840,6841,6843,6844,6845,6846,6847,6848,6850,6851,6852,6853,6854,7139,7141,7143,7145,7146,7147,7148,7149,7150,7151,7152,7153,7154,7155,7156,7157,7159,7160,7161,7162,7163,7164,7165,7167,7171,7343,7344,7345,7346,7347,7348,7352,7353,7354,8013,8015,8017,8019,8021,8023,8024,8025,8026,8028,8030,8032,8033,8034,8035,8036,8037,8038,8039,8040,8041,8042,8043,8046,8047,8048,8049,8051,8053,8054,8056,8057,8059,8061,8063,8064,8065,8066,8067,8068,8069,8070,8071,8072,8073,8074,8076,8077,8078,8079,8081,8082,8083,8084,8085,8087,8089,8091,8093,8094,8095,8096,8097,8098,8099,8100,8101,8102,8103,8104,8105,8106,8107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7276,11821,11974,30140,32323,32378,32502,32566,32636,32697,32772,32935,33012,33399,33484,33617,33693,34180,34257,34335,34441,34547,34626,34706,34763,38846,38920,38995,39060,39349,39409,39577,39649,40684,40751,55394,55511,55628,55745,55862,55921,55975,56029,56082,56136,56190,56244,61607,61681,61870,61943,62066,62137,62209,62281,62562,62619,63467,63540,63614,63688,64709,64781,64854,64924,65435,65495,66029,66098,66167,66237,66311,66387,66451,66528,66604,66681,66746,66815,66892,66967,67036,67104,67181,67247,67308,67405,67470,67539,67638,67709,67768,67826,67883,67942,68006,68077,68149,68221,68293,68365,68432,68500,68568,68627,68690,68754,68844,68935,68995,69061,69128,69194,69264,69328,69381,69448,69509,69576,69689,69747,69810,69875,69940,70015,70088,70160,70204,70251,70297,70346,70407,70468,70529,70591,70655,70719,70783,70848,70911,70971,71032,71098,71157,71217,71279,71350,71410,76197,76283,76898,76988,77075,77163,77245,77328,77418,129601,129653,129711,129756,129822,129886,129943,130000,132454,132511,132559,132608,132861,134110,134157,134590,136261,136700,136764,136954,137014,141703,141777,141847,141925,141979,142049,142134,142182,142228,142289,142352,142418,142482,142553,142616,142681,142745,142806,142867,142919,142992,143066,143135,143210,143284,143358,143499,183897,190469,190547,191595,191683,191779,193288,193870,193959,194206,194487,195153,195438,197247,197724,197946,198168,198444,198671,198901,199131,199361,199591,199818,200237,200463,200888,201118,201546,201765,202048,202256,202387,202614,203040,203265,203692,203913,204338,204458,204734,205035,205359,205650,205964,206101,206232,206337,206579,206746,206950,207158,207429,207541,207653,207758,209851,210065,210211,210351,210437,210785,210873,211119,211537,211786,211868,211966,212623,212723,212975,213399,213654,220420,220509,220746,222770,223012,223114,223367,358936,369617,371133,381828,383356,385113,385739,386159,387420,388685,388941,389177,389724,390218,390823,391021,391601,392969,393344,393462,394000,394157,394353,394626,394882,395052,395193,395257,395622,395989,396665,396929,397267,397620,397714,397900,398206,398468,398593,398720,398959,399170,399289,399482,399659,400114,400295,400417,400676,400789,400976,401078,401185,401314,401589,402097,402593,403470,403764,404334,404483,405215,405387,405471,405807,405899,440210,445441,451156,451218,451796,452380,453638,453751,453980,454140,454292,454463,454629,454798,454965,455128,455371,455541,455714,455885,456159,456358,456563,467302,467386,467482,467578,467676,467776,467878,467980,468082,468184,468286,468386,468482,468594,468723,468846,468977,469108,469206,469320,469414,469554,469688,469784,469896,469996,470112,470208,470320,470420,470560,470696,470860,470990,471148,471298,471439,471583,471718,471830,471980,472108,472236,472372,472504,472634,472764,472876,490534,490680,490824,490962,491028,491118,491194,491298,491388,491490,491598,491706,491806,491886,491978,492076,492186,492238,492316,492422,492514,492618,492728,492850,493013,506158,506238,506338,506428,506538,506628,506869,506963,507069,554979,555079,555191,555305,555421,555537,555631,555745,555857,555959,556079,556201,556283,556387,556507,556633,556731,556825,556913,557025,557141,557263,557375,557550,557666,557752,557844,557956,558080,558147,558273,558341,558469,558613,558741,558810,558905,559020,559133,559232,559341,559452,559563,559664,559769,559869,559999,560090,560213,560307,560419,560505,560609,560705,560793,560911,561015,561119,561245,561333,561441,561541,561631,561741,561825,561927,562011,562065,562129,562235,562321,562431,562515", "endLines": "148,238,241,588,641,642,644,645,646,647,648,651,652,658,659,661,662,671,672,673,674,675,676,677,678,739,740,741,742,748,749,752,753,772,773,972,974,976,978,980,981,982,983,984,985,986,987,1062,1063,1066,1067,1069,1070,1071,1072,1077,1078,1093,1094,1095,1096,1116,1117,1118,1119,1129,1130,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1298,1299,1311,1312,1313,1314,1315,1316,1317,1992,1993,1994,1995,1996,1997,1998,1999,2037,2038,2039,2040,2045,2068,2069,2078,2110,2119,2120,2123,2124,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2672,2780,2781,2799,2800,2801,2830,2831,2835,2839,2843,2855,2861,2891,2895,2899,2904,2908,2912,2916,2920,2924,2928,2934,2938,2944,2948,2954,2958,2963,2967,2970,2974,2980,2984,2990,2994,3000,3003,3007,3011,3015,3019,3023,3024,3025,3026,3029,3032,3035,3038,3042,3043,3044,3045,3046,3089,3091,3093,3095,3100,3101,3105,3111,3115,3116,3118,3130,3131,3135,3141,3145,3146,3241,3245,3272,3276,3277,3281,3309,5255,5281,5452,5478,5509,5517,5523,5539,5561,5566,5571,5581,5590,5599,5603,5610,5629,5636,5637,5646,5649,5652,5656,5660,5664,5667,5668,5673,5678,5688,5693,5700,5706,5707,5710,5714,5719,5721,5723,5726,5729,5731,5735,5738,5745,5748,5751,5755,5757,5761,5763,5765,5767,5771,5779,5787,5799,5805,5814,5817,5828,5831,5832,5837,5838,5843,6456,6526,6531,6541,6550,6551,6576,6580,6583,6586,6589,6592,6595,6598,6601,6605,6608,6611,6614,6618,6621,6625,6629,6799,6800,6801,6802,6803,6804,6805,6806,6807,6808,6809,6810,6811,6812,6813,6814,6815,6816,6817,6818,6820,6822,6823,6824,6825,6826,6827,6828,6829,6831,6832,6834,6835,6837,6839,6840,6842,6843,6844,6845,6846,6847,6849,6850,6851,6852,6853,6854,7140,7142,7144,7145,7146,7147,7148,7149,7150,7151,7152,7153,7154,7155,7156,7158,7159,7160,7161,7162,7163,7164,7166,7170,7174,7343,7344,7345,7346,7347,7351,7352,7353,7354,8014,8016,8018,8020,8022,8023,8024,8025,8027,8029,8031,8032,8033,8034,8035,8036,8037,8038,8039,8040,8041,8042,8045,8046,8047,8048,8050,8052,8053,8055,8056,8058,8060,8062,8063,8064,8065,8066,8067,8068,8069,8070,8071,8072,8073,8075,8076,8077,8078,8080,8081,8082,8083,8084,8086,8088,8090,8092,8093,8094,8095,8096,8097,8098,8099,8100,8101,8102,8103,8104,8105,8106,8107", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "7326,11861,12018,30176,32373,32435,32561,32631,32692,32767,32843,33007,33085,33479,33561,33688,33764,34252,34330,34436,34542,34621,34701,34758,34816,38915,38990,39055,39121,39404,39465,39644,39717,40746,40814,55448,55565,55682,55799,55916,55970,56024,56077,56131,56185,56239,56293,61676,61755,61938,62012,62132,62204,62276,62349,62614,62672,63535,63609,63683,63758,64776,64849,64919,64990,65490,65551,66093,66162,66232,66306,66382,66446,66523,66599,66676,66741,66810,66887,66962,67031,67099,67176,67242,67303,67400,67465,67534,67633,67704,67763,67821,67878,67937,68001,68072,68144,68216,68288,68360,68427,68495,68563,68622,68685,68749,68839,68930,68990,69056,69123,69189,69259,69323,69376,69443,69504,69571,69684,69742,69805,69870,69935,70010,70083,70155,70199,70246,70292,70341,70402,70463,70524,70586,70650,70714,70778,70843,70906,70966,71027,71093,71152,71212,71274,71345,71405,71473,76278,76365,76983,77070,77158,77240,77323,77413,77504,129648,129706,129751,129817,129881,129938,129995,130049,132506,132554,132603,132654,132890,134152,134201,134631,136288,136759,136821,137009,137066,141772,141842,141920,141974,142044,142129,142177,142223,142284,142347,142413,142477,142548,142611,142676,142740,142801,142862,142914,142987,143061,143130,143205,143279,143353,143494,143564,183945,190542,190632,191678,191774,191864,193865,193954,194201,194482,194734,195433,195826,197719,197941,198163,198439,198666,198896,199126,199356,199586,199813,200232,200458,200883,201113,201541,201760,202043,202251,202382,202609,203035,203260,203687,203908,204333,204453,204729,205030,205354,205645,205959,206096,206227,206332,206574,206741,206945,207153,207424,207536,207648,207753,207870,210060,210206,210346,210432,210780,210868,211114,211532,211781,211863,211961,212618,212718,212970,213394,213649,213743,220504,220741,222765,223007,223109,223362,225518,369612,371128,381823,383351,385108,385734,386154,387415,388680,388936,389172,389719,390213,390818,391016,391596,392964,393339,393457,393995,394152,394348,394621,394877,395047,395188,395252,395617,395984,396660,396924,397262,397615,397709,397895,398201,398463,398588,398715,398954,399165,399284,399477,399654,400109,400290,400412,400671,400784,400971,401073,401180,401309,401584,402092,402588,403465,403759,404329,404478,405210,405382,405466,405802,405894,406172,445436,450807,451213,451791,452375,452466,453746,453975,454135,454287,454458,454624,454793,454960,455123,455366,455536,455709,455880,456154,456353,456558,456888,467381,467477,467573,467671,467771,467873,467975,468077,468179,468281,468381,468477,468589,468718,468841,468972,469103,469201,469315,469409,469549,469683,469779,469891,469991,470107,470203,470315,470415,470555,470691,470855,470985,471143,471293,471434,471578,471713,471825,471975,472103,472231,472367,472499,472629,472759,472871,473011,490675,490819,490957,491023,491113,491189,491293,491383,491485,491593,491701,491801,491881,491973,492071,492181,492233,492311,492417,492509,492613,492723,492845,493008,493165,506233,506333,506423,506533,506623,506864,506958,507064,507156,555074,555186,555300,555416,555532,555626,555740,555852,555954,556074,556196,556278,556382,556502,556628,556726,556820,556908,557020,557136,557258,557370,557545,557661,557747,557839,557951,558075,558142,558268,558336,558464,558608,558736,558805,558900,559015,559128,559227,559336,559447,559558,559659,559764,559864,559994,560085,560208,560302,560414,560500,560604,560700,560788,560906,561010,561114,561240,561328,561436,561536,561626,561736,561820,561922,562006,562060,562124,562230,562316,562426,562510,562630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e803a21e40f4f47a7be5edc22c63580a\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2117", "startColumns": "4", "startOffsets": "136586", "endColumns": "49", "endOffsets": "136631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\45823f9be481063fd3d75aa15b23a5cb\\transformed\\recyclerview-1.3.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "519,1307,1308,1309,1322,1323,1324,2047", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "26034,76692,76751,76799,77701,77776,77852,132949", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "26085,76746,76794,76850,77771,77847,77919,133010"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "3147,7197", "startColumns": "4,4", "startOffsets": "213748,494312", "endLines": "3164,7197", "endColumns": "12,69", "endOffsets": "214619,494377"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2222,2223,2224,2226,2227,2228,2229,2230,2231,2232,2233,2235,2236,2238,2239,2240,2250,2251,2262,2270,2271,2272,2273,2274,2275,2276,2277,2281,2282,2283,2285,2286,2287,2288,2289,2309,2310,2311,2312,2313,2314,2315,2316,2317,2319,2320,2321,2322,2324,2325,2326,2327,2328,2329,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2348,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2360,2361,2365,2366,2367,2368,2371,2373,2374,2386,2387,2388,2389,2390,2396,2399,2400,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2449,2450,2456,2457,2480,2481,2551,2552,2553,2554,2555,2556,2557,2558,2559,2560,2561,2562,2563,2564,2565,2566,2567,2568,2569,2570,2571,2572,2573,2574,2575,2576,2577,2578,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2619,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2634,2635,2636,2637,2638,2639,2640,2641,2642,2643,2644,2645,2646,2647,2650,2651,2652,2653,2654,2655,2656,2657,2658,2659,2660,2661,2662,2663,2664,2665,2666,2669,2670,2671,2673,2674,2680,2681,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2692,2693,2694,2695,2696,2697,2698,2699,2700,2701,2702,2707,2708,2709,2710,2711,2712,2713,2716,2717,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2745,2746,2747,2748,2749,2751,2752,2753,2754,2755,2756,2757,2758,2759,2760,2761,2762,2763,2764,2765,2766,2767,2768", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "143609,143672,143807,143969,144005,144057,144120,144170,144232,144284,144320,144483,144533,144734,144774,144827,146101,146212,147036,147585,147627,147727,147769,147823,147861,147922,147982,148391,148437,148540,148669,148709,148751,148805,148857,151294,151364,151426,151497,151574,151624,151675,151725,151787,151888,151936,151974,152038,152163,152225,152328,152390,152444,152512,152789,152839,152885,152933,152979,153025,153103,153149,153200,153271,153319,153427,153627,153772,153840,153914,153996,154064,154155,154237,154317,154391,154465,154541,154599,155060,155132,155190,155269,155467,155573,155634,156357,156399,156461,156509,156573,157141,157386,157424,157628,157695,157758,157827,157894,157958,158040,158117,158190,158281,158346,158431,158507,158616,158656,158745,158791,158879,158941,159021,159066,159140,159222,159273,159313,159386,159440,159498,159566,159608,161574,161633,161994,162056,165242,165308,175866,175914,175990,176064,176118,176172,176268,176361,176415,176453,176521,176579,176656,176718,176810,176870,176993,177065,177129,177187,177253,177307,177358,177422,177488,177562,177619,177674,177779,177813,177851,177911,177965,178025,178081,178135,178191,178240,178292,178340,178394,178448,178532,178588,178646,178705,178759,178815,178868,178926,178968,179049,179098,179134,179213,179267,179349,179408,179488,179550,179645,179712,179766,179812,179879,179973,180130,180926,180990,181054,181122,181230,181305,181405,181459,181536,181602,181703,181761,181813,181877,181930,181974,182014,182066,182116,182172,182215,182259,182350,182439,182688,182734,182793,182850,182897,182995,183039,183126,183168,183220,183264,183330,183380,183438,183487,183554,183608,183771,183817,183855,183950,184023,184616,184668,184738,184808,184868,184928,185021,185075,185143,185209,185275,185327,185387,185433,185478,185531,185610,185678,185738,185792,185846,185892,185945,186324,186370,186410,186464,186520,186568,186614,187020,187064,187274,187329,187384,187439,187497,187566,187621,187690,187739,187796,187843,187901,187962,188022,188089,188193,188235,188281,188331,188375,188417,188463,188501,188594,188634,188686,188734,188778,188904,188991,189037,189095,189156,189198,189262,189311,189368,189417,189463,189511,189572,189626,189674,189718,189773,189824", "endColumns": "62,134,61,35,51,62,49,61,51,35,79,49,50,39,52,37,110,37,37,41,99,41,53,37,60,59,110,45,102,49,39,41,53,51,59,69,61,70,76,49,50,49,61,53,47,37,63,55,61,102,61,53,67,118,49,45,47,45,45,77,45,50,70,47,107,39,79,67,73,81,67,90,81,79,73,73,75,57,57,71,57,78,55,41,60,65,41,61,47,63,43,51,37,49,66,62,68,66,63,81,76,72,90,64,84,75,49,39,88,45,87,61,79,44,73,81,50,39,72,53,57,67,41,48,58,97,61,53,65,41,47,75,73,53,53,95,92,53,37,67,57,76,61,91,59,122,71,63,57,65,53,50,63,65,73,56,54,55,33,37,59,53,59,55,53,55,48,51,47,53,53,83,55,57,58,53,55,52,57,41,80,48,35,78,53,81,58,79,61,94,66,53,45,66,93,78,71,63,63,67,107,74,99,53,76,65,100,57,51,63,52,43,39,51,49,55,42,43,90,88,108,45,58,56,46,97,43,86,41,51,43,65,49,57,48,66,53,55,45,37,41,72,84,51,69,69,59,59,92,53,67,65,65,51,59,45,44,52,78,67,59,53,53,45,52,92,45,39,53,55,47,45,57,43,50,54,54,54,57,68,54,68,48,56,46,57,60,59,66,103,41,45,49,43,41,45,37,47,39,51,47,43,81,86,45,57,60,41,63,48,56,48,45,47,60,53,47,43,54,50,61", "endOffsets": "143667,143802,143864,144000,144052,144115,144165,144227,144279,144315,144395,144528,144579,144769,144822,144860,146207,146245,147069,147622,147722,147764,147818,147856,147917,147977,148088,148432,148535,148585,148704,148746,148800,148852,148912,151359,151421,151492,151569,151619,151670,151720,151782,151836,151931,151969,152033,152089,152220,152323,152385,152439,152507,152626,152834,152880,152928,152974,153020,153098,153144,153195,153266,153314,153422,153462,153702,153835,153909,153991,154059,154150,154232,154312,154386,154460,154536,154594,154652,155127,155185,155264,155320,155504,155629,155695,156394,156456,156504,156568,156612,157188,157419,157469,157690,157753,157822,157889,157953,158035,158112,158185,158276,158341,158426,158502,158552,158651,158740,158786,158874,158936,159016,159061,159135,159217,159268,159308,159381,159435,159493,159561,159603,159652,161628,161726,162051,162105,165303,165345,175909,175985,176059,176113,176167,176263,176356,176410,176448,176516,176574,176651,176713,176805,176865,176988,177060,177124,177182,177248,177302,177353,177417,177483,177557,177614,177669,177725,177808,177846,177906,177960,178020,178076,178130,178186,178235,178287,178335,178389,178443,178527,178583,178641,178700,178754,178810,178863,178921,178963,179044,179093,179129,179208,179262,179344,179403,179483,179545,179640,179707,179761,179807,179874,179968,180047,180197,180985,181049,181117,181225,181300,181400,181454,181531,181597,181698,181756,181808,181872,181925,181969,182009,182061,182111,182167,182210,182254,182345,182434,182543,182729,182788,182845,182892,182990,183034,183121,183163,183215,183259,183325,183375,183433,183482,183549,183603,183659,183812,183850,183892,184018,184103,184663,184733,184803,184863,184923,185016,185070,185138,185204,185270,185322,185382,185428,185473,185526,185605,185673,185733,185787,185841,185887,185940,186033,186365,186405,186459,186515,186563,186609,186667,187059,187110,187324,187379,187434,187492,187561,187616,187685,187734,187791,187838,187896,187957,188017,188084,188188,188230,188276,188326,188370,188412,188458,188496,188544,188629,188681,188729,188773,188855,188986,189032,189090,189151,189193,189257,189306,189363,189412,189458,189506,189567,189621,189669,189713,189768,189819,189881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\4edb4fa237ded69772f2a19f3222541d\\transformed\\library-1.6.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "544,545,546,665,666,679,703,750,751,769,770,959,960,961,962,963,964,965,966,967,968,969,970,971,973,975,977,979,1034,1035,1075,1076,1107,1114,1115,1120,1127,1128,1131,1134,1222,1229,1236,1247,1970,2193,2330,2331,2332,10167,10173,10187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "27387,27449,27524,33854,33906,34821,36373,39470,39523,40527,40579,54664,54714,54771,54821,54878,54932,54993,55044,55102,55154,55212,55277,55336,55453,55570,55687,55804,59939,59987,62452,62503,64246,64590,64646,64995,65341,65387,65556,65709,71478,71840,72220,72939,128502,141656,152631,152680,152748,696660,697049,697864", "endLines": "544,545,546,665,666,679,703,750,751,769,770,959,960,961,962,963,964,965,966,967,968,969,970,971,973,975,977,979,1034,1035,1075,1076,1107,1114,1115,1120,1127,1128,1131,1134,1222,1229,1236,1247,1970,2193,2330,2331,2332,10172,10186,10198", "endColumns": "61,74,72,51,59,51,49,52,53,51,59,49,56,49,56,53,60,50,57,51,57,64,58,57,57,57,57,57,47,49,50,58,54,55,62,44,45,47,62,54,43,50,49,48,53,46,48,67,40,12,12,12", "endOffsets": "27444,27519,27592,33901,33961,34868,36418,39518,39572,40574,40634,54709,54766,54816,54873,54927,54988,55039,55097,55149,55207,55272,55331,55389,55506,55623,55740,55857,59982,60032,62498,62557,64296,64641,64704,65035,65382,65430,65614,65759,71517,71886,72265,72983,128551,141698,152675,152743,152784,697044,697859,698513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\6ae4eed1b1ae105f01d3e7330e2833b6\\transformed\\material-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "21,27,38,41,42,43,44,45,48,49,50,51,52,54,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,128,129,151,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,237,239,240,242,243,244,245,246,247,248,249,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,478,479,480,508,520,521,522,523,524,525,526,527,528,529,530,531,535,536,537,538,540,541,542,543,547,548,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,589,590,601,602,612,613,643,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1036,1037,1038,1039,1040,1041,1042,1043,1223,1235,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,2000,2001,2050,2051,2052,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2071,2074,2075,2076,2094,2095,2096,2097,2098,2099,2100,2111,2121,2122,2125,2126,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2187,2189,2237,2244,2245,2246,2247,2248,2249,2278,2279,2280,2284,2345,2349,2362,2363,2364,2397,2401,2415,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2494,2497,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2618,2620,2621,2622,2623,2675,2678,2679,2714,2715,2802,2806,2810,2814,2818,2819,2862,2870,2877,3047,3050,3060,3069,3078,3165,3166,3167,3168,3174,3175,3176,3177,3178,3179,3185,3186,3187,3188,3189,3194,3195,3199,3200,3206,3210,3211,3212,3213,3223,3224,3225,3229,3230,3236,3240,3310,3313,3314,3319,3320,3323,3324,3325,3326,3590,3597,3858,3864,4128,4135,4396,4402,4465,4547,4599,4681,4743,4825,4889,4941,5023,5031,5037,5048,5052,5056,5069,5844,5860,5867,5873,5890,5903,5923,5940,5949,5954,5961,5981,5994,6011,6017,6023,6030,6034,6040,6054,6057,6067,6068,6069,6117,6121,6125,6129,6130,6131,6134,6150,6157,6171,6216,6244,6250,6254,6258,6263,6270,6276,6277,6280,6284,6289,6302,6306,6311,6316,6321,6324,6327,6330,6334,6527,6528,6529,6530,6642,6643,6644,6645,6646,6647,6648,6649,6650,6651,6652,6653,6654,6655,6656,6657,6658,6659,6660,6664,6668,6672,6676,6680,6684,6688,6689,6690,6691,6692,6693,6694,6695,6699,6703,6704,6708,6709,6712,6716,6719,6722,6725,6729,6732,6735,6739,6743,6747,6751,6754,6755,6756,6757,6760,6764,6767,6770,6773,6776,6779,6782,6866,6869,6870,6873,6876,6877,6880,6881,6882,6886,6887,6892,6899,6906,6913,6920,6927,6934,6941,6948,6955,6964,6973,6982,6989,6998,7007,7010,7013,7014,7015,7016,7017,7018,7019,7020,7021,7022,7023,7024,7025,7029,7034,7039,7042,7043,7044,7045,7046,7054,7062,7063,7071,7075,7083,7091,7099,7107,7115,7116,7124,7132,7133,7136,7175,7177,7182,7184,7189,7193,7198,7199,7200,7201,7205,7209,7210,7214,7215,7216,7217,7218,7219,7220,7221,7222,7223,7224,7228,7229,7230,7231,7235,7236,7237,7238,7242,7246,7247,7251,7252,7253,7258,7259,7260,7261,7262,7263,7264,7265,7266,7267,7268,7269,7270,7271,7272,7273,7274,7275,7276,7277,7278,7282,7283,7284,7290,7291,7295,7297,7298,7303,7304,7305,7306,7307,7308,7312,7313,7314,7320,7321,7325,7327,7331,7335,7339,7355,7356,7357,7358,7361,7364,7367,7370,7373,7378,7382,7385,7386,7391,7395,7400,7406,7412,7417,7421,7426,7430,7434,7475,7476,7477,7478,7479,7483,7484,7485,7486,7490,7494,7498,7502,7506,7510,7514,7518,7524,7525,7566,7580,7585,7611,7618,7621,7632,7637,7640,7643,7698,7704,7705,7708,7711,7714,7717,7720,7723,7726,7730,7733,7734,7735,7743,7751,7754,7759,7764,7769,7774,7778,7782,7783,7791,7792,7793,7794,7795,7803,7808,7813,7814,7815,7816,7841,7847,7852,7855,7859,7862,7866,7876,7879,7884,7887,7891,8110,8118,8132,8145,8149,8164,8175,8178,8189,8194,8198,8233,8234,8235,8247,8255,8263,8271,8279,8299,8302,8329,8334,8354,8357,8360,8367,8380,8389,8392,8412,8422,8426,8430,8443,8447,8451,8455,8461,8465,8482,8490,8494,8498,8502,8505,8509,8513,8517,8527,8534,8541,8545,8571,8581,8606,8615,8635,8645,8649,8659,8684,8694,8697,8704,8711,8718,8719,8720,8721,8722,8729,8733,8739,8745,8746,8759,8760,8761,8764,8767,8770,8773,8776,8779,8782,8785,8788,8791,8794,8797,8800,8803,8806,8809,8812,8815,8818,8821,8824,8825,8833,8841,8842,8855,8865,8869,8874,8879,8883,8886,8890,8894,8897,8901,8904,8908,8913,8918,8921,8928,8932,8936,8945,8950,8955,8956,8960,8963,8967,8980,8985,8993,8997,9001,9018,9022,9027,9045,9052,9056,9086,9089,9092,9095,9098,9101,9104,9123,9129,9137,9144,9156,9164,9169,9177,9181,9199,9206,9222,9226,9234,9237,9242,9243,9244,9245,9249,9253,9257,9261,9296,9299,9303,9307,9341,9344,9348,9352,9361,9367,9370,9380,9384,9385,9392,9396,9403,9404,9405,9408,9413,9418,9419,9423,9438,9457,9461,9462,9474,9484,9485,9497,9502,9526,9529,9535,9538,9547,9555,9559,9562,9565,9568,9572,9575,9592,9596,9599,9614,9617,9625,9630,9637,9642,9643,9648,9649,9655,9661,9667,9699,9710,9727,9734,9738,9741,9754,9763,9767,9772,9776,9780,9784,9788,9792,9796,9800,9805,9808,9820,9825,9834,9837,9844,9845,9849,9858,9864,9868,9869,9873,9894,9900,9904,9908,9909,9927,9928,9929,9930,9931,9936,9939,9940,9946,9947,9959,9971,9978,9979,9984,9989,9990,9994,10008,10013,10019,10025,10031,10036,10042,10048,10049,10055,10070,10075,10084,10093,10096,10110,10115,10126,10130,10139,10148,10149,10156,14587,14588,14589,14590,14591,14592,14593,14594,14595,14596,14597,14598,14599,14600,14601,14602,14603,14604,14605,14606,14607,14608,14609,14610,14611,14612,14613,14614,14615,14616,14617,14618,14619,14620,14621,14622,14623,14624,14625,14626,14627,14628,14629,14630,14631,14632,14633,14634,14635,14636,14637,14638,14639,14640,14641,14642,14643,14644,14645,14646,14647,14648,14649,14650,14651,14652,14653,14654,14655,14656,14657,14658,14659,14660,14661,14662,14663,14664,14665,14666,14667,14668,14669,14670,14671,14672,14673,14674,14675,14676,14677,14678,14679,14680,14681,14682,14683,14684,14685,14686,14687,14688,14689,14690,14691,14692,14693,14694,14695,14696,14697,14698,14699,14700,14701,14702,14703,14704,14705,14706,14707,14708,14709,14710,14711,14712,14713,14714,14715,14716,14717,14718,14719,14720,14721,14722,14723,14724,14725,14726,14727,14728,14729,14730,14731,14732,14733,14734,14735,14736,14737,14738,14739,14740,14741,14742,14743,14744,14745,14746,14747,14748,14749,14750,14751,14752,14753,14754,14755,14756,14757,14758,14759,14760,14761,14762,14763,14764,14765,14766,14767,14768,14769,14770,14771,14772,14773,14774,14775,14776,14777,14778,14779,14780,14781,14782,14783,14784,14785,14786,14787,14788,14789,14790,14791,14792,14793,14794,14795,14796,14797,14798,14799,14800,14801,14802,14803,14804,14805,14806,14807,14808,14809,14810,14811,14812,14813,14814,14815,14816,14817,14818,14819,14820,14821,14822,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949,14950,14951,14952,14953,14954,14955,14956,14957,14958,14959,14960,14961", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "825,1067,1520,1678,1734,1794,1855,1920,2075,2125,2175,2228,2286,2385,2755,2803,2874,2946,3018,3091,3158,3207,3261,3298,3349,3409,3456,3512,3561,3619,3673,3734,3790,3841,3901,3957,4020,4069,4125,4181,4231,4290,4345,4407,4454,4508,4564,4616,4671,4725,4779,4833,4882,4940,4994,5051,5107,5154,5207,5263,5323,5386,5445,5507,5557,5611,5665,5713,5770,5823,6447,6501,7432,7543,7605,7661,7721,7774,7835,7914,7995,8067,8146,8226,8302,8380,8449,8525,8602,8673,8746,8822,8900,8969,9045,9122,9186,9257,11770,11866,11919,12023,12090,12143,12195,12245,12303,12368,12416,19357,19424,19490,19548,19617,19675,19744,19814,19887,19961,20029,20096,20166,20232,20305,20365,20441,20501,20561,20636,20704,20770,20838,20898,20957,21014,21080,21142,21199,21267,21340,21410,21472,21533,21601,21663,21733,21802,21858,21917,21979,22041,22108,22165,22226,22287,22348,22409,22465,22521,22577,22633,22691,22749,22807,22865,22922,22979,23036,23093,23152,23211,23269,23352,23435,23508,23562,23631,23687,23768,23849,23920,24255,24308,24366,25531,26090,26136,26196,26250,26320,26390,26455,26521,26586,26654,26723,26791,26921,26974,27033,27091,27189,27239,27291,27337,27597,27653,27747,27805,27863,27925,27988,28050,28109,28169,28234,28300,28365,28427,28489,28551,28613,28675,28737,28803,28870,28936,28999,29063,29126,29194,29255,29317,29379,29442,29506,29569,29633,29711,29770,29836,29916,29977,30181,30239,30672,30717,31184,31248,32440,36423,36497,36568,36634,36708,36777,36848,36921,36992,37060,37133,37209,37279,37357,37425,37491,37552,37621,37685,37751,37819,37885,37948,38016,38087,38152,38225,38288,38369,38433,38499,38569,38639,38709,38779,41571,41628,41686,41745,41805,41864,41923,41982,42041,42100,42159,42218,42277,42336,42395,42455,42516,42578,42639,42700,42761,42822,42883,42944,43004,43065,43126,43186,43247,43308,43369,43430,43491,43552,43613,43674,43735,43796,43857,43925,43994,44064,44133,44202,44271,44340,44409,44478,44547,44616,44685,44754,44814,44875,44937,44998,45059,45120,45181,45242,45303,45364,45425,45486,45547,45609,45672,45736,45799,45862,45925,45988,46051,46114,46177,46240,46303,46366,46427,46489,46552,46614,46676,46738,46800,46862,46924,46986,47048,47110,47172,47229,47315,47395,47485,47580,47672,47764,47854,47937,48030,48117,48214,48305,48406,48493,48596,48685,48784,48876,48976,49060,49154,49242,49340,49423,49514,49608,49707,49809,49907,50007,50094,50194,50280,50376,50464,50545,50636,50732,50825,50918,51009,51094,51188,51277,51375,51468,51570,51658,51762,51853,51953,52046,52147,52232,52327,52416,52515,52600,52692,52787,52887,52990,53089,53192,53281,53382,53469,53566,53654,53750,53842,53942,54032,54130,54215,54304,54393,54486,54573,56298,56364,56440,56509,56588,56661,56741,56821,56898,56966,57044,57120,57191,57272,57345,57428,57503,57588,57661,57742,57823,57897,57981,58051,58129,58199,58279,58357,58429,58511,58581,58658,58738,58823,58911,58995,59082,59156,59234,59312,59383,59464,59555,59638,59734,59832,60037,60102,60168,60221,60297,60363,60450,60526,71522,72165,73097,73151,73230,73308,73381,73446,73509,73575,73646,73717,73787,73849,73918,73984,74044,74111,74178,74234,74285,74338,74390,74444,74515,74578,74637,74699,74758,74831,74898,74968,75028,75091,75166,75238,75334,75405,75461,75532,75589,75646,75712,75776,75847,75904,75957,76020,76072,76130,77924,77993,78059,78118,78201,78260,78317,78384,78454,78528,78590,78659,78729,78828,78925,79024,79110,79196,79277,79352,79441,79532,79616,79675,79721,79787,79844,79911,79968,80050,80115,80181,80304,80388,80509,80574,80636,80734,80808,80891,80980,81044,81123,81197,81259,81355,81420,81479,81535,81591,81651,81758,81805,81865,81926,81990,82051,82111,82169,82212,82261,82313,82364,82416,82465,82514,82579,82645,82705,82766,82822,82881,82930,82978,83036,83093,83195,83252,83327,83375,83426,83488,83553,83605,83679,83742,83805,83873,83923,83985,84045,84102,84162,84211,84279,84385,84487,84556,84627,84683,84732,84832,84903,85013,85104,85186,85284,85340,85441,85551,85650,85713,85819,85896,86008,86135,86247,86374,86444,86558,86689,86786,86854,86972,87075,87193,87254,87328,87395,87500,87622,87696,87763,87873,87972,88045,88142,88264,88382,88500,88561,88683,88800,88868,88974,89076,89156,89227,89323,89390,89464,89538,89624,89714,89792,89869,89969,90040,90161,90282,90346,90471,90545,90669,90793,90860,90969,91097,91209,91288,91366,91467,91538,91660,91782,91847,91973,92085,92191,92259,92358,92462,92525,92591,92675,92788,92901,93019,93097,93169,93305,93441,93526,93666,93804,93942,94084,94166,94252,94329,94402,94511,94622,94750,94878,95010,95140,95270,95404,95493,95555,95651,95718,95835,95956,96053,96135,96222,96309,96440,96571,96706,96783,96860,96971,97085,97159,97268,97380,97447,97520,97585,97687,97783,97887,97955,98020,98114,98186,98296,98402,98475,98566,98668,98771,98866,98973,99078,99200,99322,99448,99507,99565,99689,99813,99941,100059,100177,100299,100385,100482,100616,100750,100830,100968,101100,101232,101368,101443,101519,101622,101696,101809,101890,101947,102008,102067,102127,102185,102246,102304,102354,102403,102470,102529,102588,102637,102708,102792,102862,102933,103013,103082,103145,103213,103279,103347,103412,103478,103555,103633,103739,103845,103941,104070,104159,104286,104352,104422,104508,104574,104657,104731,104829,104925,105021,105119,105228,105323,105412,105474,105534,105599,105656,105737,105791,105848,105945,106055,106116,106231,106352,106447,106539,106632,106688,106747,106796,106888,106937,106991,107045,107099,107153,107207,107262,107372,107482,107590,107700,107810,107920,108030,108138,108244,108348,108452,108556,108651,108746,108839,108932,109036,109142,109246,109350,109443,109536,109629,109722,109830,109936,110042,110148,110245,110340,110435,110530,110636,110742,110848,110954,111052,111147,111243,111340,111405,111509,111890,111954,112015,112077,112137,112202,112264,112332,112390,112453,112516,112583,112658,112731,112797,112849,112902,112954,113011,113095,113190,113275,113356,113436,113513,113592,113669,113743,113817,113888,113968,114040,114115,114180,114241,114301,114376,114450,114523,114593,114665,114735,114808,114872,114942,114988,115057,115109,115194,115277,115334,115400,115467,115533,115614,115689,115745,115798,115859,115917,115967,116016,116065,116114,116176,116228,116273,116354,116405,116459,116512,116566,116617,116666,116732,116783,116844,116905,116967,117017,117058,117135,117194,117253,117312,117373,117429,117485,117552,117613,117678,117733,117798,117867,117935,118013,118082,118142,118213,118287,118352,118424,118494,118561,118645,118714,118781,118851,118914,118981,119049,119132,119211,119301,119378,119446,119513,119591,119648,119705,119773,119839,119895,119955,120014,120068,120118,120168,120216,120278,120329,120402,120482,120562,120626,120693,120764,120822,120883,120949,121008,121075,121135,121195,121258,121326,121387,121454,121532,121602,121651,121708,121777,121838,121926,122014,122102,122190,122277,122364,122451,122538,122596,122670,122740,122796,122867,122932,122994,123069,123142,123232,123298,123364,123425,123489,123551,123609,123680,123763,123822,123893,123959,124024,124085,124144,124215,124281,124346,124429,124505,124580,124661,124721,124790,124860,124929,124984,125040,125096,125157,125215,125271,125330,125384,125439,125501,125558,125652,125721,125822,125873,125943,126006,126062,126120,126179,126233,126319,126403,126473,126542,126612,126727,126848,126915,126982,127057,127124,127183,127237,127291,127345,127398,127450,130054,130191,133085,133134,133184,133275,133323,133379,133437,133499,133554,133612,133683,133747,133806,133868,133934,134248,134393,134437,134482,135460,135511,135558,135603,135654,135705,135756,136293,136826,136892,137071,137134,137274,137331,137385,137440,137498,137553,137612,137668,137737,137806,137875,137945,138008,138071,138134,138197,138262,138327,138392,138457,138520,138584,138648,138712,138763,138841,138919,138990,139062,139135,139207,139273,139339,139407,139475,139541,139608,139682,139745,139802,139862,139927,139994,140059,140116,140177,140235,140339,140449,140558,140662,140740,140805,140872,140938,141008,141055,141107,141209,141336,144584,145012,145143,145327,145505,145743,145932,148093,148191,148306,148590,153467,153707,154657,154746,154903,157193,157474,158557,159657,159844,159940,160030,160126,160216,160382,160505,160628,160798,160904,161019,161134,161236,161342,161459,162110,162192,162365,162533,162681,162840,162995,163168,163285,163402,163570,163682,163796,163968,164144,164302,164435,164547,164693,164845,164977,165120,165350,165528,165664,165760,165896,165991,166158,166251,166343,166530,166686,166864,167028,167210,167527,167709,167891,168081,168313,168503,168680,168842,168999,169109,169292,169429,169633,169817,170001,170161,170319,170503,170730,170933,171104,171324,171546,171701,171901,172085,172188,172378,172519,172684,172855,173055,173259,173461,173626,173831,174030,174229,174426,174517,174666,174816,174900,175049,175194,175346,175487,175653,180052,180202,180503,180669,180824,184108,184266,184430,186672,186895,191869,192146,192418,192696,192941,193003,195831,196282,196738,207875,208023,208537,208974,209408,214624,214709,214830,214929,215334,215431,215548,215635,215758,215859,216265,216364,216483,216576,216683,217026,217133,217378,217499,217908,218156,218256,218361,218480,218989,219136,219255,219506,219639,220054,220308,225523,225770,225895,226303,226424,226652,226773,226906,227053,247775,248267,268738,269162,289929,290423,310939,311365,316206,321623,325714,331145,335887,341264,345248,349240,354631,355178,355611,356367,356597,356840,358007,406177,407081,407665,408138,409568,410312,411505,412559,413037,413330,413713,415228,415993,417136,417577,418018,418614,418888,419299,420315,420493,421246,421383,421474,423668,423934,424256,424466,424575,424694,424878,425996,426466,427217,429800,431076,431452,431680,431936,432195,432771,433125,433247,433386,433678,433938,434866,435152,435555,435957,436300,436512,436713,436926,437215,450812,450885,450972,451057,457551,457663,457769,457892,458024,458147,458277,458401,458534,458665,458790,458907,459027,459159,459287,459401,459519,459632,459753,459941,460128,460309,460492,460676,460841,461023,461143,461263,461371,461481,461593,461701,461811,461976,462142,462294,462459,462560,462680,462851,463012,463175,463336,463503,463622,463739,463919,464101,464282,464465,464620,464765,464887,465022,465185,465378,465504,465656,465798,465968,466124,466296,473774,473969,474061,474234,474396,474491,474660,474754,474843,475086,475175,475468,475884,476304,476725,477151,477568,477984,478401,478819,479233,479703,480176,480648,481059,481530,482002,482192,482398,482504,482612,482718,482830,482944,483056,483170,483286,483400,483508,483618,483726,483988,484367,484771,484918,485026,485136,485244,485358,485767,486181,486297,486715,486956,487386,487821,488231,488653,489063,489185,489594,490010,490132,490350,493170,493238,493582,493662,494018,494168,494382,494458,494570,494660,494922,495187,495295,495447,495555,495631,495743,495833,495935,496043,496151,496251,496359,496444,496610,496714,496842,496929,497096,497174,497288,497380,497644,497911,498021,498174,498284,498368,498757,498855,498963,499057,499187,499295,499417,499553,499661,499781,499915,500037,500165,500307,500433,500573,500699,500817,500949,501047,501157,501457,501569,501687,502151,502267,502570,502696,502792,503193,503303,503427,503565,503675,503797,504109,504233,504363,504839,504967,505282,505420,505582,505798,505954,507161,507229,507313,507417,507620,507809,508010,508203,508408,508721,508933,509099,509215,509461,509677,509990,510416,510878,511115,511267,511527,511671,511813,515045,515159,515279,515395,515489,515810,515909,516027,516128,516407,516692,516971,517253,517506,517765,518018,518274,518698,518774,522024,523379,523823,525677,526252,526460,527470,527850,528016,528157,533177,533603,533715,533850,534003,534200,534371,534554,534729,534916,535188,535346,535430,535534,536021,536577,536735,536954,537185,537408,537643,537865,538131,538269,538868,538982,539120,539232,539356,539927,540422,540968,541113,541206,541298,543225,543795,544093,544282,544488,544681,544891,545775,545920,546312,546470,546687,562774,563206,564081,564701,564898,565846,566611,566734,567507,567728,567928,569905,570005,570095,570781,571534,572299,573062,573837,575050,575215,576828,577149,578212,578422,578592,579162,580057,580690,580856,582342,582958,583194,583415,584373,584638,584903,585150,585564,585800,587085,587534,587721,587970,588212,588388,588629,588862,589087,589682,590157,590681,590942,592293,592768,593994,594464,595512,595964,596208,596665,597910,598393,598543,599098,599550,599950,600103,600248,600391,600461,600889,601177,601681,602190,602306,603208,603330,603442,603619,603885,604155,604421,604689,604945,605205,605461,605719,605971,606227,606479,606733,606965,607201,607453,607709,607961,608215,608447,608681,608793,609445,609900,610024,611116,611931,612127,612451,612840,613192,613433,613647,613946,614138,614453,614660,615006,615306,615707,615926,616339,616576,616946,617670,618025,618294,618434,618688,618832,619109,620101,620510,621142,621488,621856,622930,623293,623693,625201,625786,626104,628639,628833,629051,629277,629489,629688,629895,631099,631394,631951,632341,632973,633450,633695,634182,634428,635624,636021,637027,637249,637672,637863,638242,638330,638438,638546,638859,639184,639503,639834,642537,642725,642986,643235,645819,646011,646276,646529,647061,647469,647668,648252,648487,648611,649023,649237,649639,649742,649872,650047,650299,650495,650635,650829,651840,652909,653197,653327,654104,654761,654907,655613,655851,657391,657541,657958,658123,658809,659279,659475,659566,659650,659794,660028,660195,661123,661409,661569,662184,662343,662671,662898,663410,663772,663851,664190,664295,664660,665031,665392,667266,667895,668971,669395,669648,669800,670848,671585,671788,672034,672281,672499,672741,673062,673326,673631,673854,674165,674354,675069,675338,675832,676058,676498,676657,676941,677686,678051,678356,678514,678752,680071,680469,680697,680917,681059,682349,682455,682585,682723,682847,683135,683304,683404,683689,683803,684686,685441,685880,686004,686250,686443,686577,686768,687547,687765,688056,688335,688652,688874,689169,689452,689556,689897,690713,691029,691590,692096,692301,693087,693492,694153,694342,694893,695459,695579,695981,838056,838151,838244,838307,838389,838482,838575,838662,838760,838851,838942,839030,839114,839210,839310,839416,839519,839620,839724,839830,839929,840035,840137,840244,840353,840464,840595,840715,840831,840949,841048,841155,841271,841390,841518,841607,841702,841779,841868,841959,842052,842126,842223,842318,842416,842515,842619,842715,842817,842920,843020,843123,843208,843309,843407,843497,843592,843679,843785,843887,843981,844072,844166,844242,844334,844423,844526,844637,844720,844806,844901,844998,845094,845182,845283,845384,845487,845593,845691,845788,845883,845981,846084,846184,846287,846392,846510,846626,846721,846814,846899,846995,847089,847181,847264,847368,847473,847573,847674,847779,847879,847980,848079,848181,848275,848382,848484,848587,848680,848776,848878,848981,849077,849179,849282,849379,849482,849580,849684,849789,849886,849994,850108,850223,850331,850445,850560,850662,850767,850875,850985,851101,851218,851313,851410,851509,851614,851720,851819,851924,852030,852130,852236,852337,852444,852563,852662,852767,852869,852971,853071,853174,853269,853373,853458,853562,853666,853764,853868,853974,854072,854177,854275,854388,854482,854571,854660,854743,854834,854917,855015,855105,855201,855290,855384,855472,855568,855653,855761,855862,855963,856061,856167,856258,856357,856454,856552,856648,856741,856851,856949,857044,857154,857246,857346,857445,857532,857636,857741,857840,857947,858054,858153,858262,858354,858465,858576,858687,858791,858906,859022,859149,859269,859364,859459,859556,859655,859747,859846,859938,860037,860123,860217,860320,860416,860519,860615,860718,860815,860913,861016,861109,861199,861300,861383,861474,861559,861651,861754,861849,861945,862038,862132,862211,862318,862409,862508,862601,862704,862808,862909,863010,863114,863208,863312,863416,863529,863635,863741,863849,863966,864067,864175,864275,864378,864483,864590,864686,864765,864855,864939,865031,865104,865196,865285,865377,865462,865559,865652,865747,865846,865943,866034,866125,866217,866312,866419,866527,866629,866726,866823,866916,867003,867087,867184,867281,867374,867461,867552,867651,867750,867845,867934,868015,868114,868218,868315,868420,868517,868601,868700,868804,868901,869006,869103,869201,869302,869408,869507,869614,869713,869812,869903,869992,870081,870163,870256,870347,870458,870559,870659,870771,870884,870982,871090,871184,871284,871373,871465,871576,871686,871781,871897,872023,872149,872268,872396,872521,872646,872764,872891,873000,873109,873222,873345,873468,873584,873709,873806,873914,874036,874152,874268,874377,874465,874566,874655,874756,874843,874931,875028,875120,875226,875326,875402", "endLines": "21,27,38,41,42,43,44,45,48,49,50,51,52,54,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,128,129,151,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,237,239,240,242,243,244,245,246,247,248,249,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,473,478,479,480,508,520,521,522,523,524,525,526,527,528,529,530,534,535,536,537,538,540,541,542,543,547,548,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,589,590,601,602,612,613,643,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1036,1037,1038,1039,1040,1041,1042,1043,1223,1235,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,2000,2001,2050,2051,2052,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2071,2074,2075,2076,2094,2095,2096,2097,2098,2099,2100,2111,2121,2122,2125,2126,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2187,2192,2237,2244,2245,2246,2247,2248,2249,2278,2279,2280,2284,2347,2349,2362,2363,2364,2397,2401,2415,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2482,2483,2484,2485,2486,2487,2488,2489,2490,2493,2496,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2618,2620,2621,2622,2623,2677,2678,2679,2714,2715,2805,2809,2813,2817,2818,2822,2869,2876,2884,3049,3059,3068,3077,3086,3165,3166,3167,3173,3174,3175,3176,3177,3178,3184,3185,3186,3187,3188,3193,3194,3198,3199,3205,3209,3210,3211,3212,3222,3223,3224,3228,3229,3235,3239,3240,3312,3313,3318,3319,3322,3323,3324,3325,3589,3596,3857,3863,4127,4134,4395,4401,4464,4546,4598,4680,4742,4824,4888,4940,5022,5030,5036,5047,5051,5055,5068,5083,5859,5866,5872,5889,5902,5922,5939,5948,5953,5960,5980,5993,6010,6016,6022,6029,6033,6039,6053,6056,6066,6067,6068,6116,6120,6124,6128,6129,6130,6133,6149,6156,6170,6215,6216,6249,6253,6257,6262,6269,6275,6276,6279,6283,6288,6301,6305,6310,6315,6320,6323,6326,6329,6333,6337,6527,6528,6529,6530,6642,6643,6644,6645,6646,6647,6648,6649,6650,6651,6652,6653,6654,6655,6656,6657,6658,6659,6663,6667,6671,6675,6679,6683,6687,6688,6689,6690,6691,6692,6693,6694,6698,6702,6703,6707,6708,6711,6715,6718,6721,6724,6728,6731,6734,6738,6742,6746,6750,6753,6754,6755,6756,6759,6763,6766,6769,6772,6775,6778,6781,6785,6868,6869,6872,6875,6876,6879,6880,6881,6885,6886,6891,6898,6905,6912,6919,6926,6933,6940,6947,6954,6963,6972,6981,6988,6997,7006,7009,7012,7013,7014,7015,7016,7017,7018,7019,7020,7021,7022,7023,7024,7028,7033,7038,7041,7042,7043,7044,7045,7053,7061,7062,7070,7074,7082,7090,7098,7106,7114,7115,7123,7131,7132,7135,7138,7176,7181,7183,7188,7192,7196,7198,7199,7200,7204,7208,7209,7213,7214,7215,7216,7217,7218,7219,7220,7221,7222,7223,7227,7228,7229,7230,7234,7235,7236,7237,7241,7245,7246,7250,7251,7252,7257,7258,7259,7260,7261,7262,7263,7264,7265,7266,7267,7268,7269,7270,7271,7272,7273,7274,7275,7276,7277,7281,7282,7283,7289,7290,7294,7296,7297,7302,7303,7304,7305,7306,7307,7311,7312,7313,7319,7320,7324,7326,7330,7334,7338,7342,7355,7356,7357,7360,7363,7366,7369,7372,7377,7381,7384,7385,7390,7394,7399,7405,7411,7416,7420,7425,7429,7433,7474,7475,7476,7477,7478,7482,7483,7484,7485,7489,7493,7497,7501,7505,7509,7513,7517,7523,7524,7565,7579,7584,7610,7617,7620,7631,7636,7639,7642,7697,7703,7704,7707,7710,7713,7716,7719,7722,7725,7729,7732,7733,7734,7742,7750,7753,7758,7763,7768,7773,7777,7781,7782,7790,7791,7792,7793,7794,7802,7807,7812,7813,7814,7815,7840,7846,7851,7854,7858,7861,7865,7875,7878,7883,7886,7890,7894,8117,8131,8144,8148,8163,8174,8177,8188,8193,8197,8232,8233,8234,8246,8254,8262,8270,8278,8298,8301,8328,8333,8353,8356,8359,8366,8379,8388,8391,8411,8421,8425,8429,8442,8446,8450,8454,8460,8464,8481,8489,8493,8497,8501,8504,8508,8512,8516,8526,8533,8540,8544,8570,8580,8605,8614,8634,8644,8648,8658,8683,8693,8696,8703,8710,8717,8718,8719,8720,8721,8728,8732,8738,8744,8745,8758,8759,8760,8763,8766,8769,8772,8775,8778,8781,8784,8787,8790,8793,8796,8799,8802,8805,8808,8811,8814,8817,8820,8823,8824,8832,8840,8841,8854,8864,8868,8873,8878,8882,8885,8889,8893,8896,8900,8903,8907,8912,8917,8920,8927,8931,8935,8944,8949,8954,8955,8959,8962,8966,8979,8984,8992,8996,9000,9017,9021,9026,9044,9051,9055,9085,9088,9091,9094,9097,9100,9103,9122,9128,9136,9143,9155,9163,9168,9176,9180,9198,9205,9221,9225,9233,9236,9241,9242,9243,9244,9248,9252,9256,9260,9295,9298,9302,9306,9340,9343,9347,9351,9360,9366,9369,9379,9383,9384,9391,9395,9402,9403,9404,9407,9412,9417,9418,9422,9437,9456,9460,9461,9473,9483,9484,9496,9501,9525,9528,9534,9537,9546,9554,9558,9561,9564,9567,9571,9574,9591,9595,9598,9613,9616,9624,9629,9636,9641,9642,9647,9648,9654,9660,9666,9698,9709,9726,9733,9737,9740,9753,9762,9766,9771,9775,9779,9783,9787,9791,9795,9799,9804,9807,9819,9824,9833,9836,9843,9844,9848,9857,9863,9867,9868,9872,9893,9899,9903,9907,9908,9926,9927,9928,9929,9930,9935,9938,9939,9945,9946,9958,9970,9977,9978,9983,9988,9989,9993,10007,10012,10018,10024,10030,10035,10041,10047,10048,10054,10069,10074,10083,10092,10095,10109,10114,10125,10129,10138,10147,10148,10155,10163,14587,14588,14589,14590,14591,14592,14593,14594,14595,14596,14597,14598,14599,14600,14601,14602,14603,14604,14605,14606,14607,14608,14609,14610,14611,14612,14613,14614,14615,14616,14617,14618,14619,14620,14621,14622,14623,14624,14625,14626,14627,14628,14629,14630,14631,14632,14633,14634,14635,14636,14637,14638,14639,14640,14641,14642,14643,14644,14645,14646,14647,14648,14649,14650,14651,14652,14653,14654,14655,14656,14657,14658,14659,14660,14661,14662,14663,14664,14665,14666,14667,14668,14669,14670,14671,14672,14673,14674,14675,14676,14677,14678,14679,14680,14681,14682,14683,14684,14685,14686,14687,14688,14689,14690,14691,14692,14693,14694,14695,14696,14697,14698,14699,14700,14701,14702,14703,14704,14705,14706,14707,14708,14709,14710,14711,14712,14713,14714,14715,14716,14717,14718,14719,14720,14721,14722,14723,14724,14725,14726,14727,14728,14729,14730,14731,14732,14733,14734,14735,14736,14737,14738,14739,14740,14741,14742,14743,14744,14745,14746,14747,14748,14749,14750,14751,14752,14753,14754,14755,14756,14757,14758,14759,14760,14761,14762,14763,14764,14765,14766,14767,14768,14769,14770,14771,14772,14773,14774,14775,14776,14777,14778,14779,14780,14781,14782,14783,14784,14785,14786,14787,14788,14789,14790,14791,14792,14793,14794,14795,14796,14797,14798,14799,14800,14801,14802,14803,14804,14805,14806,14807,14808,14809,14810,14811,14812,14813,14814,14815,14816,14817,14818,14819,14820,14821,14822,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949,14950,14951,14952,14953,14954,14955,14956,14957,14958,14959,14960,14961", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,60,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,85,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,82,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,85,76,72,108,110,127,127,131,129,129,133,88,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,66,72,64,101,95,103,67,64,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,69,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,69,85,65,82,73,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,94,95,96,64,103,57,63,60,61,59,64,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,58,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,203,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,10,103,127,86,10,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,152,144,142,69,10,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,92,86,97,90,90,87,83,95,99,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,88,94,76,88,90,92,73,96,94,97,98,103,95,101,102,99,102,84,100,97,89,94,86,105,101,93,90,93,75,91,88,102,110,82,85,94,96,95,87,100,100,102,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,82,103,104,99,100,104,99,100,98,101,93,106,101,102,92,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,94,96,98,104,105,98,104,105,99,105,100,106,118,98,104,101,101,99,102,94,103,84,103,103,97,103,105,97,104,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,94,94,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,92,89,100,82,90,84,91,102,94,95,92,93,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,100,107,99,102,104,106,95,78,89,83,91,72,91,88,91,84,96,92,94,98,96,90,90,91,94,106,107,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,103,96,104,96,83,98,103,96,104,96,97,100,105,98,106,98,98,90,88,88,81,92,90,110,100,99,111,112,97,107,93,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,121,115,115,108,87,100,88,100,86,87,96,91,105,99,75,76", "endOffsets": "876,1111,1570,1729,1789,1850,1915,1970,2120,2170,2223,2281,2329,2449,2798,2869,2941,3013,3086,3153,3202,3256,3293,3344,3404,3451,3507,3556,3614,3668,3729,3785,3836,3896,3952,4015,4064,4120,4176,4226,4285,4340,4402,4449,4503,4559,4611,4666,4720,4774,4828,4877,4935,4989,5046,5102,5149,5202,5258,5318,5381,5440,5502,5552,5606,5660,5708,5765,5818,5874,6496,6552,7490,7600,7656,7716,7769,7830,7909,7990,8062,8141,8221,8297,8375,8444,8520,8597,8668,8741,8817,8895,8964,9040,9117,9181,9252,9324,11816,11914,11969,12085,12138,12190,12240,12298,12363,12411,12462,19419,19485,19543,19612,19670,19739,19809,19882,19956,20024,20091,20161,20227,20300,20360,20436,20496,20556,20631,20699,20765,20833,20893,20952,21009,21075,21137,21194,21262,21335,21405,21467,21528,21596,21658,21728,21797,21853,21912,21974,22036,22103,22160,22221,22282,22343,22404,22460,22516,22572,22628,22686,22744,22802,22860,22917,22974,23031,23088,23147,23206,23264,23347,23430,23503,23557,23626,23682,23763,23844,23915,24044,24303,24361,24419,25584,26131,26191,26245,26315,26385,26450,26516,26581,26649,26718,26786,26916,26969,27028,27086,27138,27234,27286,27332,27382,27648,27695,27800,27858,27920,27983,28045,28104,28164,28229,28295,28360,28422,28484,28546,28608,28670,28732,28798,28865,28931,28994,29058,29121,29189,29250,29312,29374,29437,29501,29564,29628,29706,29765,29831,29911,29972,30025,30234,30285,30712,30773,31243,31302,32497,36492,36563,36629,36703,36772,36843,36916,36987,37055,37128,37204,37274,37352,37420,37486,37547,37616,37680,37746,37814,37880,37943,38011,38082,38147,38220,38283,38364,38428,38494,38564,38634,38704,38774,38841,41623,41681,41740,41800,41859,41918,41977,42036,42095,42154,42213,42272,42331,42390,42450,42511,42573,42634,42695,42756,42817,42878,42939,42999,43060,43121,43181,43242,43303,43364,43425,43486,43547,43608,43669,43730,43791,43852,43920,43989,44059,44128,44197,44266,44335,44404,44473,44542,44611,44680,44749,44809,44870,44932,44993,45054,45115,45176,45237,45298,45359,45420,45481,45542,45604,45667,45731,45794,45857,45920,45983,46046,46109,46172,46235,46298,46361,46422,46484,46547,46609,46671,46733,46795,46857,46919,46981,47043,47105,47167,47224,47310,47390,47480,47575,47667,47759,47849,47932,48025,48112,48209,48300,48401,48488,48591,48680,48779,48871,48971,49055,49149,49237,49335,49418,49509,49603,49702,49804,49902,50002,50089,50189,50275,50371,50459,50540,50631,50727,50820,50913,51004,51089,51183,51272,51370,51463,51565,51653,51757,51848,51948,52041,52142,52227,52322,52411,52510,52595,52687,52782,52882,52985,53084,53187,53276,53377,53464,53561,53649,53745,53837,53937,54027,54125,54210,54299,54388,54481,54568,54659,56359,56435,56504,56583,56656,56736,56816,56893,56961,57039,57115,57186,57267,57340,57423,57498,57583,57656,57737,57818,57892,57976,58046,58124,58194,58274,58352,58424,58506,58576,58653,58733,58818,58906,58990,59077,59151,59229,59307,59378,59459,59550,59633,59729,59827,59934,60097,60163,60216,60292,60358,60445,60521,60597,71582,72215,73146,73225,73303,73376,73441,73504,73570,73641,73712,73782,73844,73913,73979,74039,74106,74173,74229,74280,74333,74385,74439,74510,74573,74632,74694,74753,74826,74893,74963,75023,75086,75161,75233,75329,75400,75456,75527,75584,75641,75707,75771,75842,75899,75952,76015,76067,76125,76192,77988,78054,78113,78196,78255,78312,78379,78449,78523,78585,78654,78724,78823,78920,79019,79105,79191,79272,79347,79436,79527,79611,79670,79716,79782,79839,79906,79963,80045,80110,80176,80299,80383,80504,80569,80631,80729,80803,80886,80975,81039,81118,81192,81254,81350,81415,81474,81530,81586,81646,81753,81800,81860,81921,81985,82046,82106,82164,82207,82256,82308,82359,82411,82460,82509,82574,82640,82700,82761,82817,82876,82925,82973,83031,83088,83190,83247,83322,83370,83421,83483,83548,83600,83674,83737,83800,83868,83918,83980,84040,84097,84157,84206,84274,84380,84482,84551,84622,84678,84727,84827,84898,85008,85099,85181,85279,85335,85436,85546,85645,85708,85814,85891,86003,86130,86242,86369,86439,86553,86684,86781,86849,86967,87070,87188,87249,87323,87390,87495,87617,87691,87758,87868,87967,88040,88137,88259,88377,88495,88556,88678,88795,88863,88969,89071,89151,89222,89318,89385,89459,89533,89619,89709,89787,89864,89964,90035,90156,90277,90341,90466,90540,90664,90788,90855,90964,91092,91204,91283,91361,91462,91533,91655,91777,91842,91968,92080,92186,92254,92353,92457,92520,92586,92670,92783,92896,93014,93092,93164,93300,93436,93521,93661,93799,93937,94079,94161,94247,94324,94397,94506,94617,94745,94873,95005,95135,95265,95399,95488,95550,95646,95713,95830,95951,96048,96130,96217,96304,96435,96566,96701,96778,96855,96966,97080,97154,97263,97375,97442,97515,97580,97682,97778,97882,97950,98015,98109,98181,98291,98397,98470,98561,98663,98766,98861,98968,99073,99195,99317,99443,99502,99560,99684,99808,99936,100054,100172,100294,100380,100477,100611,100745,100825,100963,101095,101227,101363,101438,101514,101617,101691,101804,101885,101942,102003,102062,102122,102180,102241,102299,102349,102398,102465,102524,102583,102632,102703,102787,102857,102928,103008,103077,103140,103208,103274,103342,103407,103473,103550,103628,103734,103840,103936,104065,104154,104281,104347,104417,104503,104569,104652,104726,104824,104920,105016,105114,105223,105318,105407,105469,105529,105594,105651,105732,105786,105843,105940,106050,106111,106226,106347,106442,106534,106627,106683,106742,106791,106883,106932,106986,107040,107094,107148,107202,107257,107367,107477,107585,107695,107805,107915,108025,108133,108239,108343,108447,108551,108646,108741,108834,108927,109031,109137,109241,109345,109438,109531,109624,109717,109825,109931,110037,110143,110240,110335,110430,110525,110631,110737,110843,110949,111047,111142,111238,111335,111400,111504,111562,111949,112010,112072,112132,112197,112259,112327,112385,112448,112511,112578,112653,112726,112792,112844,112897,112949,113006,113090,113185,113270,113351,113431,113508,113587,113664,113738,113812,113883,113963,114035,114110,114175,114236,114296,114371,114445,114518,114588,114660,114730,114803,114867,114937,114983,115052,115104,115189,115272,115329,115395,115462,115528,115609,115684,115740,115793,115854,115912,115962,116011,116060,116109,116171,116223,116268,116349,116400,116454,116507,116561,116612,116661,116727,116778,116839,116900,116962,117012,117053,117130,117189,117248,117307,117368,117424,117480,117547,117608,117673,117728,117793,117862,117930,118008,118077,118137,118208,118282,118347,118419,118489,118556,118640,118709,118776,118846,118909,118976,119044,119127,119206,119296,119373,119441,119508,119586,119643,119700,119768,119834,119890,119950,120009,120063,120113,120163,120211,120273,120324,120397,120477,120557,120621,120688,120759,120817,120878,120944,121003,121070,121130,121190,121253,121321,121382,121449,121527,121597,121646,121703,121772,121833,121921,122009,122097,122185,122272,122359,122446,122533,122591,122665,122735,122791,122862,122927,122989,123064,123137,123227,123293,123359,123420,123484,123546,123604,123675,123758,123817,123888,123954,124019,124080,124139,124210,124276,124341,124424,124500,124575,124656,124716,124785,124855,124924,124979,125035,125091,125152,125210,125266,125325,125379,125434,125496,125553,125647,125716,125817,125868,125938,126001,126057,126115,126174,126228,126314,126398,126468,126537,126607,126722,126843,126910,126977,127052,127119,127178,127232,127286,127340,127393,127445,127519,130186,130326,133129,133179,133229,133318,133374,133432,133494,133549,133607,133678,133742,133801,133863,133929,133995,134286,134432,134477,134520,135506,135553,135598,135649,135700,135751,135802,136336,136887,136949,137129,137201,137326,137380,137435,137493,137548,137607,137663,137732,137801,137870,137940,138003,138066,138129,138192,138257,138322,138387,138452,138515,138579,138643,138707,138758,138836,138914,138985,139057,139130,139202,139268,139334,139402,139470,139536,139603,139677,139740,139797,139857,139922,139989,140054,140111,140172,140230,140334,140444,140553,140657,140735,140800,140867,140933,141003,141050,141102,141152,141261,141651,144729,145138,145322,145500,145738,145927,146096,148186,148301,148386,148664,153622,153767,154741,154898,155055,157341,157623,158611,159839,159935,160025,160121,160211,160377,160500,160623,160793,160899,161014,161129,161231,161337,161454,161569,162187,162360,162528,162676,162835,162990,163163,163280,163397,163565,163677,163791,163963,164139,164297,164430,164542,164688,164840,164972,165115,165237,165523,165659,165755,165891,165986,166153,166246,166338,166525,166681,166859,167023,167205,167522,167704,167886,168076,168308,168498,168675,168837,168994,169104,169287,169424,169628,169812,169996,170156,170314,170498,170725,170928,171099,171319,171541,171696,171896,172080,172183,172373,172514,172679,172850,173050,173254,173456,173621,173826,174025,174224,174421,174512,174661,174811,174895,175044,175189,175341,175482,175648,175809,180125,180498,180664,180819,180921,184261,184425,184611,186890,187015,192141,192413,192691,192936,192998,193283,196277,196733,197242,208018,208532,208969,209403,209846,214704,214825,214924,215329,215426,215543,215630,215753,215854,216260,216359,216478,216571,216678,217021,217128,217373,217494,217903,218151,218251,218356,218475,218984,219131,219250,219501,219634,220049,220303,220415,225765,225890,226298,226419,226647,226768,226901,227048,247770,248262,268733,269157,289924,290418,310934,311360,316201,321618,325709,331140,335882,341259,345243,349235,354626,355173,355606,356362,356592,356835,358002,358931,407076,407660,408133,409563,410307,411500,412554,413032,413325,413708,415223,415988,417131,417572,418013,418609,418883,419294,420310,420488,421241,421378,421469,423663,423929,424251,424461,424570,424689,424873,425991,426461,427212,429795,429890,431447,431675,431931,432190,432766,433120,433242,433381,433673,433933,434861,435147,435550,435952,436295,436507,436708,436921,437210,437495,450880,450967,451052,451151,457658,457764,457887,458019,458142,458272,458396,458529,458660,458785,458902,459022,459154,459282,459396,459514,459627,459748,459936,460123,460304,460487,460671,460836,461018,461138,461258,461366,461476,461588,461696,461806,461971,462137,462289,462454,462555,462675,462846,463007,463170,463331,463498,463617,463734,463914,464096,464277,464460,464615,464760,464882,465017,465180,465373,465499,465651,465793,465963,466119,466291,466582,473964,474056,474229,474391,474486,474655,474749,474838,475081,475170,475463,475879,476299,476720,477146,477563,477979,478396,478814,479228,479698,480171,480643,481054,481525,481997,482187,482393,482499,482607,482713,482825,482939,483051,483165,483281,483395,483503,483613,483721,483983,484362,484766,484913,485021,485131,485239,485353,485762,486176,486292,486710,486951,487381,487816,488226,488648,489058,489180,489589,490005,490127,490345,490529,493233,493577,493657,494013,494163,494307,494453,494565,494655,494917,495182,495290,495442,495550,495626,495738,495828,495930,496038,496146,496246,496354,496439,496605,496709,496837,496924,497091,497169,497283,497375,497639,497906,498016,498169,498279,498363,498752,498850,498958,499052,499182,499290,499412,499548,499656,499776,499910,500032,500160,500302,500428,500568,500694,500812,500944,501042,501152,501452,501564,501682,502146,502262,502565,502691,502787,503188,503298,503422,503560,503670,503792,504104,504228,504358,504834,504962,505277,505415,505577,505793,505949,506153,507224,507308,507412,507615,507804,508005,508198,508403,508716,508928,509094,509210,509456,509672,509985,510411,510873,511110,511262,511522,511666,511808,515040,515154,515274,515390,515484,515805,515904,516022,516123,516402,516687,516966,517248,517501,517760,518013,518269,518693,518769,522019,523374,523818,525672,526247,526455,527465,527845,528011,528152,533172,533598,533710,533845,533998,534195,534366,534549,534724,534911,535183,535341,535425,535529,536016,536572,536730,536949,537180,537403,537638,537860,538126,538264,538863,538977,539115,539227,539351,539922,540417,540963,541108,541201,541293,543220,543790,544088,544277,544483,544676,544886,545770,545915,546307,546465,546682,546943,563201,564076,564696,564893,565841,566606,566729,567502,567723,567923,569900,570000,570090,570776,571529,572294,573057,573832,575045,575210,576823,577144,578207,578417,578587,579157,580052,580685,580851,582337,582953,583189,583410,584368,584633,584898,585145,585559,585795,587080,587529,587716,587965,588207,588383,588624,588857,589082,589677,590152,590676,590937,592288,592763,593989,594459,595507,595959,596203,596660,597905,598388,598538,599093,599545,599945,600098,600243,600386,600456,600884,601172,601676,602185,602301,603203,603325,603437,603614,603880,604150,604416,604684,604940,605200,605456,605714,605966,606222,606474,606728,606960,607196,607448,607704,607956,608210,608442,608676,608788,609440,609895,610019,611111,611926,612122,612446,612835,613187,613428,613642,613941,614133,614448,614655,615001,615301,615702,615921,616334,616571,616941,617665,618020,618289,618429,618683,618827,619104,620096,620505,621137,621483,621851,622925,623288,623688,625196,625781,626099,628634,628828,629046,629272,629484,629683,629890,631094,631389,631946,632336,632968,633445,633690,634177,634423,635619,636016,637022,637244,637667,637858,638237,638325,638433,638541,638854,639179,639498,639829,642532,642720,642981,643230,645814,646006,646271,646524,647056,647464,647663,648247,648482,648606,649018,649232,649634,649737,649867,650042,650294,650490,650630,650824,651835,652904,653192,653322,654099,654756,654902,655608,655846,657386,657536,657953,658118,658804,659274,659470,659561,659645,659789,660023,660190,661118,661404,661564,662179,662338,662666,662893,663405,663767,663846,664185,664290,664655,665026,665387,667261,667890,668966,669390,669643,669795,670843,671580,671783,672029,672276,672494,672736,673057,673321,673626,673849,674160,674349,675064,675333,675827,676053,676493,676652,676936,677681,678046,678351,678509,678747,680066,680464,680692,680912,681054,682344,682450,682580,682718,682842,683130,683299,683399,683684,683798,684681,685436,685875,685999,686245,686438,686572,686763,687542,687760,688051,688330,688647,688869,689164,689447,689551,689892,690708,691024,691585,692091,692296,693082,693487,694148,694337,694888,695454,695574,695976,696510,838146,838239,838302,838384,838477,838570,838657,838755,838846,838937,839025,839109,839205,839305,839411,839514,839615,839719,839825,839924,840030,840132,840239,840348,840459,840590,840710,840826,840944,841043,841150,841266,841385,841513,841602,841697,841774,841863,841954,842047,842121,842218,842313,842411,842510,842614,842710,842812,842915,843015,843118,843203,843304,843402,843492,843587,843674,843780,843882,843976,844067,844161,844237,844329,844418,844521,844632,844715,844801,844896,844993,845089,845177,845278,845379,845482,845588,845686,845783,845878,845976,846079,846179,846282,846387,846505,846621,846716,846809,846894,846990,847084,847176,847259,847363,847468,847568,847669,847774,847874,847975,848074,848176,848270,848377,848479,848582,848675,848771,848873,848976,849072,849174,849277,849374,849477,849575,849679,849784,849881,849989,850103,850218,850326,850440,850555,850657,850762,850870,850980,851096,851213,851308,851405,851504,851609,851715,851814,851919,852025,852125,852231,852332,852439,852558,852657,852762,852864,852966,853066,853169,853264,853368,853453,853557,853661,853759,853863,853969,854067,854172,854270,854383,854477,854566,854655,854738,854829,854912,855010,855100,855196,855285,855379,855467,855563,855648,855756,855857,855958,856056,856162,856253,856352,856449,856547,856643,856736,856846,856944,857039,857149,857241,857341,857440,857527,857631,857736,857835,857942,858049,858148,858257,858349,858460,858571,858682,858786,858901,859017,859144,859264,859359,859454,859551,859650,859742,859841,859933,860032,860118,860212,860315,860411,860514,860610,860713,860810,860908,861011,861104,861194,861295,861378,861469,861554,861646,861749,861844,861940,862033,862127,862206,862313,862404,862503,862596,862699,862803,862904,863005,863109,863203,863307,863411,863524,863630,863736,863844,863961,864062,864170,864270,864373,864478,864585,864681,864760,864850,864934,865026,865099,865191,865280,865372,865457,865554,865647,865742,865841,865938,866029,866120,866212,866307,866414,866522,866624,866721,866818,866911,866998,867082,867179,867276,867369,867456,867547,867646,867745,867840,867929,868010,868109,868213,868310,868415,868512,868596,868695,868799,868896,869001,869098,869196,869297,869403,869502,869609,869708,869807,869898,869987,870076,870158,870251,870342,870453,870554,870654,870766,870879,870977,871085,871179,871279,871368,871460,871571,871681,871776,871892,872018,872144,872263,872391,872516,872641,872759,872886,872995,873104,873217,873340,873463,873579,873704,873801,873909,874031,874147,874263,874372,874460,874561,874650,874751,874838,874926,875023,875115,875221,875321,875397,875474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\bf2455e731a9e5bf1a1240e4ec32ba6d\\transformed\\constraintlayout-2.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "13,20,22,28,29,37,39,40,47,53,55,56,57,58,59,116,117,118,119,121,125,126,127,130,140,150,178,179,184,185,190,195,196,197,202,203,208,209,214,215,216,222,223,224,229,235,236,250,251,257,258,259,260,263,266,269,270,273,276,277,278,279,280,283,286,287,288,289,295,300,303,306,307,308,313,314,315,318,321,322,325,328,331,334,335,336,339,342,343,348,349,355,360,363,366,367,368,369,370,371,372,373,374,375,376,377,393,474,475,476,477,482,489,497,498,499,502,507,509,517,518,539,549,586,587,591,592,603,604,605,611,614,620,624,625,626,627,628,637,2053,2112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "485,769,881,1116,1177,1468,1575,1625,2027,2334,2454,2509,2569,2634,2693,5879,5931,5992,6054,6161,6294,6346,6396,6557,6964,7387,9329,9388,9585,9642,9837,10018,10072,10129,10321,10379,10575,10631,10825,10882,10933,11155,11207,11262,11452,11668,11718,12467,12523,12729,12790,12850,12920,13053,13184,13312,13380,13509,13635,13697,13760,13828,13895,14018,14143,14210,14275,14340,14629,14810,14931,15052,15118,15185,15395,15464,15530,15655,15781,15848,15974,16101,16226,16353,16409,16474,16600,16723,16788,16996,17063,17351,17531,17651,17771,17836,17898,17960,18024,18086,18145,18205,18266,18327,18386,18446,19106,24049,24100,24149,24197,24484,24776,25084,25131,25191,25297,25477,25589,25924,25978,27143,27700,30030,30081,30290,30342,30778,30837,30891,31129,31307,31509,31648,31694,31749,31794,31838,32186,133234,136341", "endLines": "19,20,26,28,36,37,39,40,47,53,55,56,57,58,59,116,117,118,119,124,125,126,127,139,147,150,178,183,184,189,194,195,196,201,202,207,208,213,214,215,221,222,223,228,234,235,236,250,256,257,258,259,262,265,268,269,272,275,276,277,278,279,282,285,286,287,288,294,299,302,305,306,307,312,313,314,317,320,321,324,327,330,333,334,335,338,341,342,347,348,354,359,362,365,366,367,368,369,370,371,372,373,374,375,376,392,398,474,475,476,477,488,496,497,498,501,506,507,516,517,518,539,549,586,587,591,600,603,604,610,611,619,623,624,625,626,627,636,640,2053,2112", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44", "endOffsets": "764,820,1062,1172,1463,1515,1620,1673,2070,2380,2504,2564,2629,2688,2750,5926,5987,6049,6095,6289,6341,6391,6442,6959,7271,7427,9383,9580,9637,9832,10013,10067,10124,10316,10374,10570,10626,10820,10877,10928,11150,11202,11257,11447,11663,11713,11765,12518,12724,12785,12845,12915,13048,13179,13307,13375,13504,13630,13692,13755,13823,13890,14013,14138,14205,14270,14335,14624,14805,14926,15047,15113,15180,15390,15459,15525,15650,15776,15843,15969,16096,16221,16348,16404,16469,16595,16718,16783,16991,17058,17346,17526,17646,17766,17831,17893,17955,18019,18081,18140,18200,18261,18322,18381,18441,19101,19352,24095,24144,24192,24250,24771,25079,25126,25186,25292,25472,25526,25919,25973,26029,27184,27742,30076,30135,30337,30667,30832,30886,31124,31179,31504,31643,31689,31744,31789,31833,32181,32318,133270,136381"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\04c78b97304896bcd5afa736fff46d1e\\transformed\\osmdroid-android-6.1.17\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2221,2225,2241,2242,2243,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2308,2318,2370,2372,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2398,2451,2452,2453,2454,2455,2550,2579,2649,2667,2668,2703,2704,2705,2706,2718,2719,2744,2750", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "143569,143869,144865,144913,144974,146250,146341,146413,146500,146570,146645,146727,146829,146875,146988,151250,151841,155407,155509,155700,155755,155816,155872,155928,155993,156050,156112,156169,156226,156292,157346,161731,161779,161821,161884,161944,175814,177730,182626,183664,183727,186038,186110,186175,186238,187115,187161,188549,188860", "endColumns": "39,99,47,60,37,90,71,86,69,74,81,101,45,112,47,43,46,59,63,54,60,55,55,64,56,61,56,56,65,64,39,47,41,62,59,49,51,48,61,62,43,71,64,62,85,45,41,44,43", "endOffsets": "143604,143964,144908,144969,145007,146336,146408,146495,146565,146640,146722,146824,146870,146983,147031,151289,151883,155462,155568,155750,155811,155867,155923,155988,156045,156107,156164,156221,156287,156352,157381,161774,161816,161879,161939,161989,175861,177774,182683,183722,183766,186105,186170,186233,186319,187156,187198,188589,188899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\0e546e96a375d1d88b2a082db553eacb\\transformed\\transition-1.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2042,2043,2066,2072,2073,2102,2103,2104,2105,2106,2107,2108,2109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "132716,132756,134000,134291,134346,135842,135887,135941,135997,136049,136101,136150,136211", "endColumns": "39,46,42,54,46,44,53,55,51,51,48,60,49", "endOffsets": "132751,132798,134038,134341,134388,135882,135936,135992,136044,136096,136145,136206,136256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\0f716dfc83cb8e5033ee3eacd43859f9\\transformed\\glide-4.16.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2044", "startColumns": "4", "startOffsets": "132803", "endColumns": "57", "endOffsets": "132856"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\98b62f2f5bc1112d0086d39f0eb418b1\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "481,653,654,680,681,1044,1045,1240,1241,1242,1243,1244,1245,1246,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2048,2049,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2101,2188,2263,2264,2265,2266,2267,2268,2269,2720,6855,6856,6860,6861,6865,8108,8109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24424,33090,33162,34873,34938,60602,60671,72451,72521,72589,72661,72731,72792,72866,127524,127585,127646,127708,127772,127834,127895,127963,128063,128123,128189,128262,128331,128388,128440,130331,130403,130479,130544,130603,130662,130722,130782,130842,130902,130962,131022,131082,131142,131202,131262,131321,131381,131441,131501,131561,131621,131681,131741,131801,131861,131921,131980,132040,132100,132159,132218,132277,132336,132395,133015,133050,134636,134691,134754,134809,134867,134925,134986,135049,135106,135157,135207,135268,135325,135391,135425,135807,141266,147074,147141,147213,147282,147351,147425,147497,187203,473016,473133,473334,473444,473645,562635,562707", "endLines": "481,653,654,680,681,1044,1045,1240,1241,1242,1243,1244,1245,1246,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2048,2049,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2101,2188,2263,2264,2265,2266,2267,2268,2269,2720,6855,6859,6860,6864,6865,8108,8109", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "24479,33157,33245,34933,34999,60666,60729,72516,72584,72656,72726,72787,72861,72934,127580,127641,127703,127767,127829,127890,127958,128058,128118,128184,128257,128326,128383,128435,128497,130398,130474,130539,130598,130657,130717,130777,130837,130897,130957,131017,131077,131137,131197,131257,131316,131376,131436,131496,131556,131616,131676,131736,131796,131856,131916,131975,132035,132095,132154,132213,132272,132331,132390,132449,133045,133080,134686,134749,134804,134862,134920,134981,135044,135101,135152,135202,135263,135320,135386,135420,135455,135837,141331,147136,147208,147277,147346,147420,147492,147580,187269,473128,473329,473439,473640,473769,562702,562769"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2391,2392,2393,2394,2395,2648", "startColumns": "4,4,4,4,4,4", "startOffsets": "156617,156699,156803,156912,157032,182548", "endColumns": "81,103,108,119,108,77", "endOffsets": "156694,156798,156907,157027,157136,182621"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\cfe645f5abe7e16c9eebab287fc0d954\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "120,10164", "startColumns": "4,4", "startOffsets": "6100,696515", "endLines": "120,10166", "endColumns": "60,12", "endOffsets": "6156,696655"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1224,1225,1226,1227,1228,1230,1231,1237,1238,1239,1248,1300,1301,1302,1303,1304,1305,1306,1310,1318,1319,1320,1321,1713,1714,1715,1716,1717,1718,1719,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "71587,71630,71687,71744,71795,71891,71940,72270,72330,72391,72988,76370,76415,76462,76507,76558,76606,76650,76855,77509,77556,77604,77651,111567,111616,111666,111715,111758,111802,111847,128556,128614,128667,128723,128779,128838,128887,128934,128981,129036,129078,129119,129172,129222,129268,129315,129363,129410,129457,129504,129555", "endColumns": "42,56,56,50,44,48,54,59,60,59,56,44,46,44,50,47,43,41,42,46,47,46,49,48,49,48,42,43,44,42,57,52,55,55,58,48,46,46,54,41,40,52,49,45,46,47,46,46,46,50,45", "endOffsets": "71625,71682,71739,71790,71835,71935,71990,72325,72386,72446,73040,76410,76457,76502,76553,76601,76645,76687,76893,77551,77599,77646,77696,111611,111661,111710,111753,111797,111842,111885,128609,128662,128718,128774,128833,128882,128929,128976,129031,129073,129114,129167,129217,129263,129310,129358,129405,129452,129499,129550,129596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d33f8fcb8e6ec6de69937c85231a8678\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "46,687,688,689,690,1232,1233,1234,2844,6217,6219,6222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1975,35259,35320,35382,35444,71995,72054,72111,194739,429895,429959,430085", "endLines": "46,687,688,689,690,1232,1233,1234,2850,6218,6221,6224", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "2022,35315,35377,35439,35503,72049,72106,72160,195148,429954,430080,430208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\00596551c31d248811f1b66873b9f7d4\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "149,152,1249", "startColumns": "4,4,4", "startOffsets": "7331,7495,73045", "endColumns": "55,47,51", "endOffsets": "7382,7538,73092"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\6e9bc98679171a20e588e1fb7c8cf1c2\\transformed\\materialish-progress-1.7\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2323", "startColumns": "4", "startOffsets": "152094", "endColumns": "68", "endOffsets": "152158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ffc1f2a80960a680f82f6ab424275895\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2046,2067", "startColumns": "4,4", "startOffsets": "132895,134043", "endColumns": "53,66", "endOffsets": "132944,134105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\be59f2a5b3dd115371e7b6a1346b6da9\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2116", "startColumns": "4", "startOffsets": "136532", "endColumns": "53", "endOffsets": "136581"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values\\arrays.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2,7", "startColumns": "4,4", "startOffsets": "150,303", "endLines": "6,12", "endColumns": "19,19", "endOffsets": "298,480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c31bb31d0c0a9ca9efeb309970d98416\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2070,2115", "startColumns": "4,4", "startOffsets": "134206,136472", "endColumns": "41,59", "endOffsets": "134243,136527"}}]}]}