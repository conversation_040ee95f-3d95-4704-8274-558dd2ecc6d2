<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="20dp"
    android:background="@color/background_dark_new">

    <ImageView
        android:id="@+id/headerIcon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_services"
        app:tint="@color/colorPrimary" />

    <TextView
        android:id="@+id/headerText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="SERVICES"
        android:textColor="@color/text_primary_dark"
        android:textSize="22sp"
        android:textStyle="bold"
        android:fontFamily="@font/kalpurush" />

    <View
        android:layout_width="0dp"
        android:layout_height="2dp"
        android:layout_weight="2"
        android:layout_marginStart="16dp"
        android:background="@drawable/gradient_blue_purple" />

</LinearLayout>
