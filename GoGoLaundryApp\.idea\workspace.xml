<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
    <layouts>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/background_pattern.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/badge_background.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/baseline_password_24.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/baseline_phone_24.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/bottom_nav_ripple.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/button_accent.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/button_primary.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/button_secondary.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/button_selector.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/button_success.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/ic_laundry_placeholder.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/search_bar_gradient_overlay.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/status_indicator_inactive.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/activity_forgot_password.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/activity_login.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/activity_login_no_otp.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/activity_main.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/activity_main_drawer.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/activity_signup.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/custom_bottom_navigation.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/custom_navigation_drawer.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/custom_toolbar.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/dialog_image_selection.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/dialog_promo_sale.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_address_picker.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_cart.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_checkout.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_home.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_invoice.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_items.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_notifications.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_order_tracking.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_orders.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_profile.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_services.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_settings.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_shop_details.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_shop_map.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_shop_services.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_cart.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_checkout.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_laundry_item.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_order.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_order_item.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_popular_laundry.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_recent_order.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_service.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_service_card.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_service_grid.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_shop_card.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_shop_details_header.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_shop_service.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_shop_service_enhanced.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_status_history.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/layout_notification_badge.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/layout_recent_orders_shimmer.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/nav_header.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/nav_header_enhanced.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/menu/bottom_nav_menu.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/menu/drawer_menu.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/xml/file_paths.xml">
        <config>
          <theme>@style/Theme.GoGoLaundry</theme>
        </config>
      </layout>
    </layouts>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="be2d0bf1-7d8e-4479-9a0d-30e9c78c6b96" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=Default, isTemplate=false, identifier=serial=192.168.0.101:5555;connection=0d350a54)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="layoutResourceFile" />
        <option value="Class" />
        <option value="resourceFile" />
      </list>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2xOFdeeEmEbLiYszG0mMlzOz2JE" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Android App.app.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.Upgrade Gradle wrapper.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_directory_selection&quot;: &quot;C:/xampp/htdocs/GoGoLaundry/GoGoLaundryApp/app/src/main/res/drawable&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/xampp/htdocs/GoGoLaundry/GoGoLaundryApp/app/src/main/res/drawable&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.augmentcode.intellij.settings&quot;,
    &quot;show.migrate.to.gradle.popup&quot;: &quot;false&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\res\drawable" />
      <recent name="C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src" />
      <recent name="C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\res" />
      <recent name="C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\res\layout" />
      <recent name="C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\java\com\mdsadrulhasan\gogolaundry\utils" />
    </key>
    <key name="android.template.-367572348">
      <recent name="com.mdsadrulhasan.gogolaundry" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.mdsadrulhasan.gogolaundry.api" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="Go_Go_Laundry.app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="RESTORE_FRESH_INSTALL_ONLY" value="false" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="be2d0bf1-7d8e-4479-9a0d-30e9c78c6b96" name="Changes" comment="" />
      <created>1747801302263</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747801302263</updated>
    </task>
    <servers />
  </component>
  <component name="play_dynamic_filters_status">
    <option name="appIdToCheckInfo">
      <map>
        <entry key="com.mdsadrulhasan.gogolaundry">
          <value>
            <CheckInfo lastCheckTimestamp="1748620234203" />
          </value>
        </entry>
        <entry key="com.mdsadrulhasan.gogolaundry.test">
          <value>
            <CheckInfo lastCheckTimestamp="1748620234211" />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>