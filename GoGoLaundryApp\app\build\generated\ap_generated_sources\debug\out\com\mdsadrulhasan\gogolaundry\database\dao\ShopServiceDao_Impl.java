package com.mdsadrulhasan.gogolaundry.database.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.mdsadrulhasan.gogolaundry.database.converters.DateConverter;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopServiceEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class ShopServiceDao_Impl implements ShopServiceDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<ShopServiceEntity> __insertionAdapterOfShopServiceEntity;

  private final EntityDeletionOrUpdateAdapter<ShopServiceEntity> __deletionAdapterOfShopServiceEntity;

  private final EntityDeletionOrUpdateAdapter<ShopServiceEntity> __updateAdapterOfShopServiceEntity;

  private final SharedSQLiteStatement __preparedStmtOfClearShopServices;

  private final SharedSQLiteStatement __preparedStmtOfClearAll;

  public ShopServiceDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfShopServiceEntity = new EntityInsertionAdapter<ShopServiceEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `shop_services` (`id`,`shop_id`,`service_id`,`is_available`,`estimated_hours`,`created_at`,`serviceName`,`serviceBnName`,`serviceImageUrl`,`description`,`bn_description`,`base_price`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final ShopServiceEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getShopId());
        statement.bindLong(3, entity.getServiceId());
        final int _tmp = entity.isAvailable() ? 1 : 0;
        statement.bindLong(4, _tmp);
        statement.bindLong(5, entity.getEstimatedHours());
        final Long _tmp_1 = DateConverter.dateToTimestamp(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, _tmp_1);
        }
        if (entity.getServiceName() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getServiceName());
        }
        if (entity.getServiceBnName() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getServiceBnName());
        }
        if (entity.getServiceImageUrl() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getServiceImageUrl());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getDescription());
        }
        if (entity.getBnDescription() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getBnDescription());
        }
        statement.bindDouble(12, entity.getBasePrice());
      }
    };
    this.__deletionAdapterOfShopServiceEntity = new EntityDeletionOrUpdateAdapter<ShopServiceEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `shop_services` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final ShopServiceEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfShopServiceEntity = new EntityDeletionOrUpdateAdapter<ShopServiceEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `shop_services` SET `id` = ?,`shop_id` = ?,`service_id` = ?,`is_available` = ?,`estimated_hours` = ?,`created_at` = ?,`serviceName` = ?,`serviceBnName` = ?,`serviceImageUrl` = ?,`description` = ?,`bn_description` = ?,`base_price` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final ShopServiceEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getShopId());
        statement.bindLong(3, entity.getServiceId());
        final int _tmp = entity.isAvailable() ? 1 : 0;
        statement.bindLong(4, _tmp);
        statement.bindLong(5, entity.getEstimatedHours());
        final Long _tmp_1 = DateConverter.dateToTimestamp(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, _tmp_1);
        }
        if (entity.getServiceName() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getServiceName());
        }
        if (entity.getServiceBnName() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getServiceBnName());
        }
        if (entity.getServiceImageUrl() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getServiceImageUrl());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getDescription());
        }
        if (entity.getBnDescription() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getBnDescription());
        }
        statement.bindDouble(12, entity.getBasePrice());
        statement.bindLong(13, entity.getId());
      }
    };
    this.__preparedStmtOfClearShopServices = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM shop_services WHERE shop_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM shop_services";
        return _query;
      }
    };
  }

  @Override
  public void insert(final ShopServiceEntity shopService) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __insertionAdapterOfShopServiceEntity.insert(shopService);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void insertAll(final List<ShopServiceEntity> shopServices) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __insertionAdapterOfShopServiceEntity.insert(shopServices);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void delete(final ShopServiceEntity shopService) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __deletionAdapterOfShopServiceEntity.handle(shopService);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void update(final ShopServiceEntity shopService) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfShopServiceEntity.handle(shopService);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void clearShopServices(final int shopId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfClearShopServices.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, shopId);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfClearShopServices.release(_stmt);
    }
  }

  @Override
  public void clearAll() {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfClearAll.acquire();
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfClearAll.release(_stmt);
    }
  }

  @Override
  public LiveData<List<ShopServiceEntity>> getServicesByShopId(final int shopId) {
    final String _sql = "SELECT ss.*, s.name as serviceName, s.bn_name as serviceBnName, s.image_url as serviceImageUrl FROM shop_services ss INNER JOIN services s ON ss.service_id = s.id WHERE ss.shop_id = ? AND ss.is_available = 1 AND s.is_active = 1 ORDER BY s.sort_order ASC, s.name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, shopId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"shop_services",
        "services"}, false, new Callable<List<ShopServiceEntity>>() {
      @Override
      @Nullable
      public List<ShopServiceEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfShopId = CursorUtil.getColumnIndexOrThrow(_cursor, "shop_id");
          final int _cursorIndexOfServiceId = CursorUtil.getColumnIndexOrThrow(_cursor, "service_id");
          final int _cursorIndexOfIsAvailable = CursorUtil.getColumnIndexOrThrow(_cursor, "is_available");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final int _cursorIndexOfServiceBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceBnName");
          final int _cursorIndexOfServiceImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceImageUrl");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBnDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_description");
          final int _cursorIndexOfBasePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "base_price");
          final List<ShopServiceEntity> _result = new ArrayList<ShopServiceEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ShopServiceEntity _item;
            _item = new ShopServiceEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final int _tmpShopId;
            _tmpShopId = _cursor.getInt(_cursorIndexOfShopId);
            _item.setShopId(_tmpShopId);
            final int _tmpServiceId;
            _tmpServiceId = _cursor.getInt(_cursorIndexOfServiceId);
            _item.setServiceId(_tmpServiceId);
            final boolean _tmpIsAvailable;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsAvailable);
            _tmpIsAvailable = _tmp != 0;
            _item.setAvailable(_tmpIsAvailable);
            final int _tmpEstimatedHours;
            _tmpEstimatedHours = _cursor.getInt(_cursorIndexOfEstimatedHours);
            _item.setEstimatedHours(_tmpEstimatedHours);
            final Date _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_1);
            _item.setCreatedAt(_tmpCreatedAt);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            _item.setServiceName(_tmpServiceName);
            final String _tmpServiceBnName;
            if (_cursor.isNull(_cursorIndexOfServiceBnName)) {
              _tmpServiceBnName = null;
            } else {
              _tmpServiceBnName = _cursor.getString(_cursorIndexOfServiceBnName);
            }
            _item.setServiceBnName(_tmpServiceBnName);
            final String _tmpServiceImageUrl;
            if (_cursor.isNull(_cursorIndexOfServiceImageUrl)) {
              _tmpServiceImageUrl = null;
            } else {
              _tmpServiceImageUrl = _cursor.getString(_cursorIndexOfServiceImageUrl);
            }
            _item.setServiceImageUrl(_tmpServiceImageUrl);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpBnDescription;
            if (_cursor.isNull(_cursorIndexOfBnDescription)) {
              _tmpBnDescription = null;
            } else {
              _tmpBnDescription = _cursor.getString(_cursorIndexOfBnDescription);
            }
            _item.setBnDescription(_tmpBnDescription);
            final double _tmpBasePrice;
            _tmpBasePrice = _cursor.getDouble(_cursorIndexOfBasePrice);
            _item.setBasePrice(_tmpBasePrice);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<ShopServiceEntity> getServicesByShopIdSync(final int shopId) {
    final String _sql = "SELECT ss.*, s.name as serviceName, s.bn_name as serviceBnName, s.image_url as serviceImageUrl FROM shop_services ss INNER JOIN services s ON ss.service_id = s.id WHERE ss.shop_id = ? AND ss.is_available = 1 AND s.is_active = 1 ORDER BY s.sort_order ASC, s.name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, shopId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfShopId = CursorUtil.getColumnIndexOrThrow(_cursor, "shop_id");
      final int _cursorIndexOfServiceId = CursorUtil.getColumnIndexOrThrow(_cursor, "service_id");
      final int _cursorIndexOfIsAvailable = CursorUtil.getColumnIndexOrThrow(_cursor, "is_available");
      final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
      final int _cursorIndexOfServiceBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceBnName");
      final int _cursorIndexOfServiceImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceImageUrl");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfBnDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_description");
      final int _cursorIndexOfBasePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "base_price");
      final List<ShopServiceEntity> _result = new ArrayList<ShopServiceEntity>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final ShopServiceEntity _item;
        _item = new ShopServiceEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final int _tmpShopId;
        _tmpShopId = _cursor.getInt(_cursorIndexOfShopId);
        _item.setShopId(_tmpShopId);
        final int _tmpServiceId;
        _tmpServiceId = _cursor.getInt(_cursorIndexOfServiceId);
        _item.setServiceId(_tmpServiceId);
        final boolean _tmpIsAvailable;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsAvailable);
        _tmpIsAvailable = _tmp != 0;
        _item.setAvailable(_tmpIsAvailable);
        final int _tmpEstimatedHours;
        _tmpEstimatedHours = _cursor.getInt(_cursorIndexOfEstimatedHours);
        _item.setEstimatedHours(_tmpEstimatedHours);
        final Date _tmpCreatedAt;
        final Long _tmp_1;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_1);
        _item.setCreatedAt(_tmpCreatedAt);
        final String _tmpServiceName;
        if (_cursor.isNull(_cursorIndexOfServiceName)) {
          _tmpServiceName = null;
        } else {
          _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
        }
        _item.setServiceName(_tmpServiceName);
        final String _tmpServiceBnName;
        if (_cursor.isNull(_cursorIndexOfServiceBnName)) {
          _tmpServiceBnName = null;
        } else {
          _tmpServiceBnName = _cursor.getString(_cursorIndexOfServiceBnName);
        }
        _item.setServiceBnName(_tmpServiceBnName);
        final String _tmpServiceImageUrl;
        if (_cursor.isNull(_cursorIndexOfServiceImageUrl)) {
          _tmpServiceImageUrl = null;
        } else {
          _tmpServiceImageUrl = _cursor.getString(_cursorIndexOfServiceImageUrl);
        }
        _item.setServiceImageUrl(_tmpServiceImageUrl);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpBnDescription;
        if (_cursor.isNull(_cursorIndexOfBnDescription)) {
          _tmpBnDescription = null;
        } else {
          _tmpBnDescription = _cursor.getString(_cursorIndexOfBnDescription);
        }
        _item.setBnDescription(_tmpBnDescription);
        final double _tmpBasePrice;
        _tmpBasePrice = _cursor.getDouble(_cursorIndexOfBasePrice);
        _item.setBasePrice(_tmpBasePrice);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<ShopServiceEntity>> getShopsByServiceId(final int serviceId) {
    final String _sql = "SELECT ss.* FROM shop_services ss INNER JOIN laundry_shops ls ON ss.shop_id = ls.id WHERE ss.service_id = ? AND ss.is_available = 1 AND ls.is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, serviceId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"shop_services",
        "laundry_shops"}, false, new Callable<List<ShopServiceEntity>>() {
      @Override
      @Nullable
      public List<ShopServiceEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfShopId = CursorUtil.getColumnIndexOrThrow(_cursor, "shop_id");
          final int _cursorIndexOfServiceId = CursorUtil.getColumnIndexOrThrow(_cursor, "service_id");
          final int _cursorIndexOfIsAvailable = CursorUtil.getColumnIndexOrThrow(_cursor, "is_available");
          final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final int _cursorIndexOfServiceBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceBnName");
          final int _cursorIndexOfServiceImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceImageUrl");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBnDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_description");
          final int _cursorIndexOfBasePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "base_price");
          final List<ShopServiceEntity> _result = new ArrayList<ShopServiceEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ShopServiceEntity _item;
            _item = new ShopServiceEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final int _tmpShopId;
            _tmpShopId = _cursor.getInt(_cursorIndexOfShopId);
            _item.setShopId(_tmpShopId);
            final int _tmpServiceId;
            _tmpServiceId = _cursor.getInt(_cursorIndexOfServiceId);
            _item.setServiceId(_tmpServiceId);
            final boolean _tmpIsAvailable;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsAvailable);
            _tmpIsAvailable = _tmp != 0;
            _item.setAvailable(_tmpIsAvailable);
            final int _tmpEstimatedHours;
            _tmpEstimatedHours = _cursor.getInt(_cursorIndexOfEstimatedHours);
            _item.setEstimatedHours(_tmpEstimatedHours);
            final Date _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_1);
            _item.setCreatedAt(_tmpCreatedAt);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            _item.setServiceName(_tmpServiceName);
            final String _tmpServiceBnName;
            if (_cursor.isNull(_cursorIndexOfServiceBnName)) {
              _tmpServiceBnName = null;
            } else {
              _tmpServiceBnName = _cursor.getString(_cursorIndexOfServiceBnName);
            }
            _item.setServiceBnName(_tmpServiceBnName);
            final String _tmpServiceImageUrl;
            if (_cursor.isNull(_cursorIndexOfServiceImageUrl)) {
              _tmpServiceImageUrl = null;
            } else {
              _tmpServiceImageUrl = _cursor.getString(_cursorIndexOfServiceImageUrl);
            }
            _item.setServiceImageUrl(_tmpServiceImageUrl);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpBnDescription;
            if (_cursor.isNull(_cursorIndexOfBnDescription)) {
              _tmpBnDescription = null;
            } else {
              _tmpBnDescription = _cursor.getString(_cursorIndexOfBnDescription);
            }
            _item.setBnDescription(_tmpBnDescription);
            final double _tmpBasePrice;
            _tmpBasePrice = _cursor.getDouble(_cursorIndexOfBasePrice);
            _item.setBasePrice(_tmpBasePrice);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public boolean doesShopOfferService(final int shopId, final int serviceId) {
    final String _sql = "SELECT COUNT(*) > 0 FROM shop_services WHERE shop_id = ? AND service_id = ? AND is_available = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, shopId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, serviceId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final boolean _result;
      if (_cursor.moveToFirst()) {
        final int _tmp;
        _tmp = _cursor.getInt(0);
        _result = _tmp != 0;
      } else {
        _result = false;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public ShopServiceEntity getShopService(final int shopId, final int serviceId) {
    final String _sql = "SELECT ss.*, s.name as serviceName, s.bn_name as serviceBnName, s.image_url as serviceImageUrl FROM shop_services ss INNER JOIN services s ON ss.service_id = s.id WHERE ss.shop_id = ? AND ss.service_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, shopId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, serviceId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfShopId = CursorUtil.getColumnIndexOrThrow(_cursor, "shop_id");
      final int _cursorIndexOfServiceId = CursorUtil.getColumnIndexOrThrow(_cursor, "service_id");
      final int _cursorIndexOfIsAvailable = CursorUtil.getColumnIndexOrThrow(_cursor, "is_available");
      final int _cursorIndexOfEstimatedHours = CursorUtil.getColumnIndexOrThrow(_cursor, "estimated_hours");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
      final int _cursorIndexOfServiceBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceBnName");
      final int _cursorIndexOfServiceImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceImageUrl");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfBnDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "bn_description");
      final int _cursorIndexOfBasePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "base_price");
      final ShopServiceEntity _result;
      if (_cursor.moveToFirst()) {
        _result = new ShopServiceEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final int _tmpShopId;
        _tmpShopId = _cursor.getInt(_cursorIndexOfShopId);
        _result.setShopId(_tmpShopId);
        final int _tmpServiceId;
        _tmpServiceId = _cursor.getInt(_cursorIndexOfServiceId);
        _result.setServiceId(_tmpServiceId);
        final boolean _tmpIsAvailable;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsAvailable);
        _tmpIsAvailable = _tmp != 0;
        _result.setAvailable(_tmpIsAvailable);
        final int _tmpEstimatedHours;
        _tmpEstimatedHours = _cursor.getInt(_cursorIndexOfEstimatedHours);
        _result.setEstimatedHours(_tmpEstimatedHours);
        final Date _tmpCreatedAt;
        final Long _tmp_1;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_1);
        _result.setCreatedAt(_tmpCreatedAt);
        final String _tmpServiceName;
        if (_cursor.isNull(_cursorIndexOfServiceName)) {
          _tmpServiceName = null;
        } else {
          _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
        }
        _result.setServiceName(_tmpServiceName);
        final String _tmpServiceBnName;
        if (_cursor.isNull(_cursorIndexOfServiceBnName)) {
          _tmpServiceBnName = null;
        } else {
          _tmpServiceBnName = _cursor.getString(_cursorIndexOfServiceBnName);
        }
        _result.setServiceBnName(_tmpServiceBnName);
        final String _tmpServiceImageUrl;
        if (_cursor.isNull(_cursorIndexOfServiceImageUrl)) {
          _tmpServiceImageUrl = null;
        } else {
          _tmpServiceImageUrl = _cursor.getString(_cursorIndexOfServiceImageUrl);
        }
        _result.setServiceImageUrl(_tmpServiceImageUrl);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _result.setDescription(_tmpDescription);
        final String _tmpBnDescription;
        if (_cursor.isNull(_cursorIndexOfBnDescription)) {
          _tmpBnDescription = null;
        } else {
          _tmpBnDescription = _cursor.getString(_cursorIndexOfBnDescription);
        }
        _result.setBnDescription(_tmpBnDescription);
        final double _tmpBasePrice;
        _tmpBasePrice = _cursor.getDouble(_cursorIndexOfBasePrice);
        _result.setBasePrice(_tmpBasePrice);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<Integer> getServiceCountByShop(final int shopId) {
    final String _sql = "SELECT COUNT(*) FROM shop_services WHERE shop_id = ? AND is_available = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, shopId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"shop_services"}, false, new Callable<Integer>() {
      @Override
      @Nullable
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
