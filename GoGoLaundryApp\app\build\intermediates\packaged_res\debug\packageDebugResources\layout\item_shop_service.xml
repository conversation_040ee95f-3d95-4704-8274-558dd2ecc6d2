<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardBackgroundColor="@color/glass_background"
    app:strokeColor="@color/white_20"
    app:strokeWidth="1dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Service Image -->
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/serviceImageView"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginEnd="16dp"
            android:scaleType="centerCrop"
            android:background="@color/white_10"
            app:shapeAppearanceOverlay="@style/RoundedImageView"
            tools:src="@drawable/ic_item_placeholder" />

        <!-- Service Details -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Service Name -->
            <TextView
                android:id="@+id/serviceNameTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/background_dark_new"
                android:textSize="16sp"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="Wash &amp; Fold" />

            <!-- Service Description -->
            <TextView
                android:id="@+id/serviceDescriptionTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/background_dark_new"
                android:textSize="12sp"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="Professional washing and folding service for your everyday clothes" />

            <!-- Estimated Time -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:src="@drawable/ic_clock"
                    app:tint="@color/white_70" />

                <TextView
                    android:id="@+id/estimatedTimeTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textColor="@color/background_dark_new"
                    android:textSize="12sp"
                    tools:text="24 hours" />

            </LinearLayout>

        </LinearLayout>

        <!-- Available Badge -->
        <TextView
            android:id="@+id/availableBadge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/badge_open"
            android:paddingHorizontal="12dp"
            android:paddingVertical="6dp"
            android:text="Available"
            android:textColor="@color/background_dark_new"
            android:textSize="10sp"
            android:textStyle="bold" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
