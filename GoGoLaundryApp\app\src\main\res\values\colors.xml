<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Primary colors -->
    <color name="primary_dark">#1565C0</color>
    <color name="primary_light">#BBDEFB</color>
    <color name="accent">#FF4081</color>
    <color name="accent_dark">#E91E63</color>
    <color name="bottom_nav_icon_tint">#FF6200EE</color> <!-- Your brand color -->
    <color name="bottom_nav_text_color">#FF6200EE</color>
    <color name="bottom_nav_ripple">#336200EE</color> <!-- Semi-transparent ripple -->

    <!-- Background colors -->
    <color name="background">#FFFFFF</color>
    <color name="background_dark">#EEEEEE</color>
    <color name="primary">#BA372E</color>
    <!-- Status colors -->
    <color name="success">#4CAF50</color>
    <color name="error">#F44336</color>
    <color name="warning">#FFC107</color>
    <color name="info">#2196F3</color>

    <!-- Other colors -->
    <color name="white">#FFFFFF</color>
    <color name="black">#000000</color>
    <color name="transparent">#00000000</color>
    <color name="gray_light">#F8756C</color>
    <color name="blue_light">#E3F2FD</color>
    <color name="colorPrimary">#BA372E</color>
    <color name="colorAccent">#BB342A</color>
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="divider">#EEEEEE</color>
    <color name="background_light">#F5F5F5</color>

    <!-- Additional colors for date and time pickers -->
    <color name="primary_light_transparent">#331976D2</color>
    <color name="stroke_color">#E0E0E0</color>
    <color name="card_border">#DDDDDD</color>

    <!-- Card colors -->
    <color name="card_background">#FFFFFF</color>
    <color name="card_stroke_color">#E0E0E0</color>
    <!-- Enhanced Date/Time Picker Colors -->
    <color name="picker_gradient_start">#4FC3F7</color>
    <color name="picker_gradient_end">#1976D2</color>
    <color name="picker_background_overlay">#F8FFFE</color>
    <color name="picker_card_background">#FFFFFF</color>
    <color name="picker_card_shadow">#1A000000</color>
    <color name="picker_selected_background">#E3F2FD</color>
    <color name="picker_selected_text">#1976D2</color>
    <color name="picker_unselected_text">#757575</color>
    <color name="picker_border_color">#E1F5FE</color>
    <color name="picker_ripple_color">#1A1976D2</color>

    <!-- Glassmorphism colors -->
    <color name="glass_background">#F0FFFFFF</color>
    <color name="glass_shadow">#0D000000</color>

    <!-- Shimmer effect colors -->
    <color name="shimmer_placeholder">#F0F0F0</color>
    <color name="shimmer_highlight">#FFFFFF</color>

    <!-- Modern gradient colors -->
    <color name="gradient_purple_start">#667eea</color>
    <color name="gradient_purple_end">#764ba2</color>

    <!-- Enhanced surface colors -->
    <color name="surface_elevated">#FFFFFF</color>
    <color name="surface_elevated_tint">#F8F9FA</color>
    <color name="on_surface_variant">#49454F</color>

    <!-- Professional Home Fragment Colors -->
    <color name="home_hero_gradient_start">#667eea</color>
    <color name="home_hero_gradient_end">#764ba2</color>
    <color name="home_card_background">#FFFFFF</color>
    <color name="home_card_shadow">#1A000000</color>
    <color name="home_glass_background">#E6FFFFFF</color>
    <color name="home_glass_border">#4DFFFFFF</color>
    <color name="home_accent_blue">#4FC3F7</color>
    <color name="home_accent_purple">#9C27B0</color>
    <color name="home_accent_green">#4CAF50</color>
    <color name="home_accent_orange">#FF9800</color>
    <color name="home_text_on_gradient">#FFFFFF</color>
    <color name="home_section_background">#FAFAFA</color>
    <color name="home_divider_subtle">#F0F0F0</color>

    <!-- Modern gradient combinations -->
    <color name="gradient_ocean_start">#667eea</color>
    <color name="gradient_ocean_end">#764ba2</color>
    <color name="gradient_sunset_start">#ff9a9e</color>
    <color name="gradient_sunset_end">#fecfef</color>
    <color name="gradient_mint_start">#a8edea</color>
    <color name="gradient_mint_end">#fed6e3</color>

    <!-- Profile Fragment Colors -->
    <color name="glass_input_background">#10FFFFFF</color>
    <color name="error_color">#F44336</color>

    <!-- Notification Fragment Colors -->
    <color name="glass_border">#30FFFFFF</color>

    <!-- Shop Map Colors -->
    <color name="white_70">#B3FFFFFF</color>
    <color name="white_50">#80FFFFFF</color>
    <color name="white_30">#4DFFFFFF</color>
    <color name="white_20">#33FFFFFF</color>
    <color name="white_10">#1AFFFFFF</color>
    <color name="colorPrimary_30">#4DBA372E</color>
    <color name="colorSecondary">#FF9800</color>
    <color name="rating_star">#FFD700</color>
    <color name="status_open">#4CAF50</color>
    <color name="status_closed">#F44336</color>
    <color name="status_busy">#FF9800</color>

    <!-- Modern Search Bar Colors -->
    <color name="search_background_primary">#66FFFFFF</color>
    <color name="search_background_secondary">#4DFFFFFF</color>
    <color name="search_border_primary">#80FFFFFF</color>
    <color name="search_border_secondary">#99FFFFFF</color>
    <color name="search_text_primary">#FFFFFF</color>
    <color name="search_text_secondary">#E0FFFFFF</color>
    <color name="search_hint_color">#B3FFFFFF</color>
    <color name="search_icon_primary">#FFFFFF</color>
    <color name="search_icon_secondary">#E0FFFFFF</color>
    <color name="search_accent_blue">#4FC3F7</color>
    <color name="search_accent_purple">#9C27B0</color>
    <color name="search_ripple_effect">#33FFFFFF</color>
    <color name="search_shadow">#1A000000</color>
    <color name="search_highlight">#CCFFFFFF</color>

    <!-- Additional gradient colors -->
    <color name="gradient_blue_start">#2196F3</color>
    <color name="gradient_blue_end">#1976D2</color>
    <color name="gradient_blue_purple">#667eea</color>

    <!-- Dark theme colors for Shop Details -->
    <color name="background_dark_new">#0F0F23</color>
    <color name="card_background_dark">#1A1A2E</color>
    <color name="text_primary_dark">#FFFFFF</color>
    <color name="text_secondary_dark">#B0B0B0</color>
    <color name="rating_background">#16213E</color>

    <!-- Modern UI colors for new design - Fixed conflicts -->
    <color name="text_hint_modern">#9E9E9E</color>
    <color name="primary_modern">#BA372E</color> <!-- Use existing brand color -->
    <color name="black_50">#80000000</color>
    <color name="divider_light">#E0E0E0</color>
    <color name="divider_dark">#424242</color>

    <!-- Modern Material Design Colors for Home Redesign -->
    <color name="card_stroke_light">#E8E8E8</color>
    <color name="text_tertiary">#9E9E9E</color>
    <color name="surface_variant">#F5F5F5</color>
    <color name="on_surface_variant_light">#6B6B6B</color>

    <!-- Enhanced primary color variations -->
    <color name="primary_container">#FFEBEE</color>
    <color name="on_primary_container">#BA372E</color>

    <!-- Surface and elevation colors -->
    <color name="surface_1">#FFFFFF</color>
    <color name="surface_2">#FAFAFA</color>
    <color name="surface_3">#F5F5F5</color>

    <!-- State colors for interactive elements -->
    <color name="state_pressed">#1ABA372E</color>
    <color name="state_focused">#26BA372E</color>
    <color name="state_hover">#0DBA372E</color>
</resources>