<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_checked="true">
        <layer-list>
            <item android:gravity="bottom|center_horizontal">
                <shape android:shape="rectangle">
                    <size android:width="24dp" android:height="3dp" />
                    <corners android:radius="1.5dp" />
                    <solid android:color="@color/primary" />
                </shape>
            </item>
        </layer-list>
    </item>
    <item android:state_pressed="true">
        <layer-list>
            <item android:gravity="bottom|center_horizontal">
                <shape android:shape="rectangle">
                    <size android:width="24dp" android:height="2dp" />
                    <corners android:radius="1dp" />
                    <solid android:color="@color/primary_light" />
                </shape>
            </item>
        </layer-list>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
</selector>
