<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:gravity="center_vertical">

    <!-- Day -->
    <TextView
        android:id="@+id/dayTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:textStyle="bold"
        tools:text="Monday" />

    <!-- Hours -->
    <TextView
        android:id="@+id/hoursTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white_70"
        android:textSize="14sp"
        tools:text="9:00 AM - 6:00 PM" />

    <!-- Status Badge -->
    <TextView
        android:id="@+id/statusTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:background="@drawable/badge_open"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        android:text="@string/open"
        android:textColor="@color/white"
        android:textSize="10sp"
        android:textStyle="bold"
        android:visibility="gone"
        tools:visibility="visible" />

</LinearLayout>
