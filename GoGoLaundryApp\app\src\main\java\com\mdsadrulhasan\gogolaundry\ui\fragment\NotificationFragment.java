package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;


import com.mdsadrulhasan.gogolaundry.MainActivity;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.adapter.NotificationAdapter;
import com.mdsadrulhasan.gogolaundry.model.Notification;
import com.mdsadrulhasan.gogolaundry.model.User;
import com.mdsadrulhasan.gogolaundry.repository.NotificationRepository;
import com.mdsadrulhasan.gogolaundry.utils.SessionManager;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;
import com.mdsadrulhasan.gogolaundry.viewmodel.NotificationViewModel;

import java.util.List;

/**
 * Fragment for displaying notifications
 */
public class NotificationFragment extends Fragment implements NotificationAdapter.NotificationClickListener {
    private static final String TAG = "NotificationFragment";

    private NotificationViewModel viewModel;
    private RecyclerView recyclerView;
    private NotificationAdapter adapter;
    private ProgressBar progressBar;
    private ConstraintLayout emptyView; // Changed from LinearLayout to ConstraintLayout
    private SwipeRefreshLayout swipeRefreshLayout;
    private com.google.android.material.switchmaterial.SwitchMaterial showReadSwitch;
    private SessionManager sessionManager;
    private User currentUser;
    private NotificationRepository notificationRepository;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        viewModel = new ViewModelProvider(this).get(NotificationViewModel.class);
        sessionManager = new SessionManager(requireContext());
        currentUser = sessionManager.getUser();
        notificationRepository = NotificationRepository.getInstance();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_notifications, container, false);

        // Initialize views
        recyclerView = view.findViewById(R.id.notifications_recycler_view);
        progressBar = view.findViewById(R.id.progress_bar);
        emptyView = view.findViewById(R.id.empty_view);
        swipeRefreshLayout = view.findViewById(R.id.swipe_refresh_layout);
        showReadSwitch = view.findViewById(R.id.show_read_switch);

        // Set up refresh button functionality
        View refreshButton = view.findViewById(R.id.refresh_button);
        if (refreshButton != null) {
            refreshButton.setOnClickListener(v -> {
                Log.d(TAG, "Refresh button clicked, refreshing notifications");
                viewModel.refreshNotifications(true);
                updateMainActivityBadge();
            });
        }

        // Set up RecyclerView
        setupRecyclerView();

        // Set up SwipeRefreshLayout
        swipeRefreshLayout.setOnRefreshListener(() -> {
            Log.d(TAG, "SwipeRefreshLayout triggered, refreshing notifications");
            viewModel.refreshNotifications(true);
            // Update badge after refresh
            updateMainActivityBadge();
        });



        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Set user ID to load notifications for
        if (currentUser != null) {
            viewModel.setUserId(currentUser.getId());
        }

        // Observe notifications data
        viewModel.getNotifications().observe(getViewLifecycleOwner(), resource -> {
            swipeRefreshLayout.setRefreshing(false);

            if (resource.isLoading()) {
                showLoading();
            } else if (resource.isSuccess()) {
                List<Notification> notifications = resource.getData();
                if (notifications != null && !notifications.isEmpty()) {
                    showNotifications(notifications);
                } else {
                    showEmpty();
                }

                // Update notification badge in MainActivity after refresh
                updateMainActivityBadge();
            } else if (resource.isError()) {
                showError(resource.getMessage());

                // Still update badge in case of error to ensure it's in sync
                updateMainActivityBadge();
            }
        });

        // Observe unread count to update notification badge
        viewModel.getUnreadCount().observe(getViewLifecycleOwner(), count -> {
            // Update notification badge in MainActivity when unread count changes
            if (getActivity() instanceof MainActivity && count != null) {
                ((MainActivity) getActivity()).updateNotificationBadge(count);
            }
        });

        // Hide the show read switch since we're always filtering out read notifications
        showReadSwitch.setVisibility(View.GONE);
    }

    /**
     * Set up RecyclerView
     */
    private void setupRecyclerView() {
        adapter = new NotificationAdapter(requireContext(), this);
        recyclerView.setAdapter(adapter);
    }

    /**
     * Show loading state
     */
    private void showLoading() {
        View progressContainer = getView().findViewById(R.id.progress_container);
        if (progressContainer != null) {
            progressContainer.setVisibility(View.VISIBLE);
        }
        recyclerView.setVisibility(View.GONE);
        emptyView.setVisibility(View.GONE);
    }

    /**
     * Show notifications
     *
     * @param notifications Notifications to display
     */
    private void showNotifications(List<Notification> notifications) {
        View progressContainer = getView().findViewById(R.id.progress_container);
        if (progressContainer != null) {
            progressContainer.setVisibility(View.GONE);
        }
        recyclerView.setVisibility(View.VISIBLE);
        emptyView.setVisibility(View.GONE);

        // Log notification status before updating adapter
        int totalCount = notifications.size();
        long readCount = notifications.stream().filter(Notification::isRead).count();
        long unreadCount = totalCount - readCount;

        Log.d(TAG, "Showing notifications - Total: " + totalCount +
              ", Read: " + readCount + ", Unread: " + unreadCount);

        // Always filter out read notifications (passing false for showReadNotifications)
        adapter.updateNotifications(notifications, false);

        // If no notifications to show, display empty view
        if (adapter.getItemCount() == 0) {
            Log.d(TAG, "No unread notifications to display, showing empty view");
            recyclerView.setVisibility(View.GONE);
            emptyView.setVisibility(View.VISIBLE);
        } else {
            Log.d(TAG, "Displaying " + adapter.getItemCount() + " unread notifications");
        }
    }

    /**
     * Show empty state
     */
    private void showEmpty() {
        View progressContainer = getView().findViewById(R.id.progress_container);
        if (progressContainer != null) {
            progressContainer.setVisibility(View.GONE);
        }
        recyclerView.setVisibility(View.GONE);
        emptyView.setVisibility(View.VISIBLE);
    }

    /**
     * Show error state
     *
     * @param message Error message
     */
    private void showError(String message) {
        View progressContainer = getView().findViewById(R.id.progress_container);
        if (progressContainer != null) {
            progressContainer.setVisibility(View.GONE);
        }

        // If we have no data, show empty view
        if (adapter.getItemCount() == 0) {
            recyclerView.setVisibility(View.GONE);
            emptyView.setVisibility(View.VISIBLE);
        }

        // Log error but don't show toast
        Log.e(TAG, "Error loading notifications: " + message);
    }



    @Override
    public void onNotificationClicked(Notification notification) {
        try {
            if (notification == null) {
                Log.e(TAG, "Cannot mark notification as read: Notification is null");
                ToastUtils.showErrorToast(requireContext(), getString(R.string.error_notification_not_found));
                viewModel.refreshNotifications(true);
                return;
            }

            // Mark as read if not already read
            if (!notification.isRead()) {
                // Show loading indicator
                View progressContainer = getView().findViewById(R.id.progress_container);
                if (progressContainer != null) {
                    progressContainer.setVisibility(View.VISIBLE);
                }

                // Log the notification ID being marked as read
                Log.d(TAG, "Marking notification as read on click: " + notification.getId());

                viewModel.markAsRead(notification.getId()).observe(getViewLifecycleOwner(), count -> {
                    if (count != null && count >= 0) {
                        // Success - notification was marked as read
                        Log.d(TAG, "Successfully marked notification as read: " + notification.getId());

                        // Update the notification in the adapter immediately
                        notification.setRead(true);

                        // Refresh the adapter - always filter out read notifications
                        List<Notification> currentNotifications = viewModel.getNotifications().getValue().getData();
                        if (currentNotifications != null) {
                            adapter.updateNotifications(currentNotifications, false);
                        }

                        // Update the badge count in MainActivity
                        if (getActivity() instanceof MainActivity) {
                            ((MainActivity) getActivity()).updateNotificationBadge(count);
                        }
                    } else {
                        // Error - could not mark notification as read
                        Log.e(TAG, "Error marking notification as read on click: " + notification.getId());
                    }

                    // Force refresh notifications from server to ensure we have the latest data
                    viewModel.refreshNotifications(true);

                    // Update badge again after refresh to ensure it's accurate
                    updateMainActivityBadge();
                });
            }

            // Handle notification click based on type
            if ("order_status".equals(notification.getType()) && notification.getOrderId() != null) {
                // Navigate to order tracking
                if (getActivity() instanceof MainActivity) {
                    try {
                        // Create and navigate to OrderTrackingFragment
                        OrderTrackingFragment trackingFragment = OrderTrackingFragment.newInstance(
                                notification.getOrderNumber(), notification.getOrderNumber());

                        // Replace current fragment with OrderTrackingFragment
                        getActivity().getSupportFragmentManager().beginTransaction()
                                .replace(R.id.fragment_container, trackingFragment)
                                .addToBackStack(null)
                                .commit();
                    } catch (Exception e) {
                        Log.e(TAG, "Error navigating to tracking fragment: " + e.getMessage());
                        Toast.makeText(getContext(), "Error viewing order: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                }
            } else {
                // For all other notification types, show notification details
                if (getActivity() instanceof MainActivity) {
                    try {
                        // Create and navigate to NotificationDetailsFragment
                        NotificationDetailsFragment detailsFragment = NotificationDetailsFragment.newInstance(notification);

                        // Replace current fragment with NotificationDetailsFragment
                        getActivity().getSupportFragmentManager().beginTransaction()
                                .replace(R.id.fragment_container, detailsFragment)
                                .addToBackStack(null)
                                .commit();
                    } catch (Exception e) {
                        Log.e(TAG, "Error navigating to notification details: " + e.getMessage());
                        Toast.makeText(getContext(), "Error viewing notification: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                }
            }
        } catch (Exception e) {
            // Handle any exceptions that might occur
            Log.e(TAG, "Exception handling notification click: " + e.getMessage(), e);

            // Refresh notifications to ensure UI is up-to-date
            viewModel.refreshNotifications();
        }
    }



    /**
     * Update the notification badge in MainActivity with the current unread count
     */
    private void updateMainActivityBadge() {
        if (getActivity() instanceof MainActivity) {
            // Get the current unread count from the ViewModel
            Integer count = viewModel.getUnreadCount().getValue();
            if (count != null) {
                // Update the badge in MainActivity
                ((MainActivity) getActivity()).updateNotificationBadge(count);
                Log.d(TAG, "Updated notification badge in MainActivity: " + count);
            } else {
                // If count is null, refresh it from the repository
                if (currentUser != null) {
                    notificationRepository.getUnreadNotificationsCount(currentUser.getId())
                            .observe(getViewLifecycleOwner(), unreadCount -> {
                                if (unreadCount != null && getActivity() instanceof MainActivity) {
                                    ((MainActivity) getActivity()).updateNotificationBadge(unreadCount);
                                    Log.d(TAG, "Updated notification badge in MainActivity after refresh: " + unreadCount);
                                }
                            });
                }
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // Refresh notifications when fragment is resumed
        if (currentUser != null) {
            viewModel.refreshNotifications(true);
            Log.d(TAG, "Refreshing notifications on resume");
        }
    }

    @Override
    public void onMarkAsReadClicked(Notification notification) {
        try {
            if (notification == null) {
                Log.e(TAG, "Cannot mark notification as read: Notification is null");
                ToastUtils.showErrorToast(requireContext(), getString(R.string.error_notification_not_found));
                viewModel.refreshNotifications(true);
                return;
            }

            // Show loading indicator
            View progressContainer = getView().findViewById(R.id.progress_container);
            if (progressContainer != null) {
                progressContainer.setVisibility(View.VISIBLE);
            }

            // Log the notification ID being marked as read
            Log.d(TAG, "Marking notification as read: " + notification.getId());

            viewModel.markAsRead(notification.getId()).observe(getViewLifecycleOwner(), count -> {
                if (count != null && count >= 0) {
                    // Success - notification was marked as read or already read
                    ToastUtils.showSuccessToast(requireContext(), getString(R.string.mark_as_read));

                    // Update the notification in the adapter immediately
                    notification.setRead(true);

                    // Refresh the adapter - always filter out read notifications
                    List<Notification> currentNotifications = viewModel.getNotifications().getValue().getData();
                    if (currentNotifications != null) {
                        adapter.updateNotifications(currentNotifications, false);
                    }

                    // Update the badge count in MainActivity
                    if (getActivity() instanceof MainActivity) {
                        ((MainActivity) getActivity()).updateNotificationBadge(count);
                    }

                    // Force refresh notifications from server to ensure we have the latest data
                    viewModel.refreshNotifications(true);

                    // Update badge again after refresh to ensure it's accurate
                    updateMainActivityBadge();
                } else {
                    // Error - could not mark notification as read
                    Log.e(TAG, "Error marking notification as read: " + notification.getId());
                    ToastUtils.showErrorToast(requireContext(), getString(R.string.error_marking_read));

                    // Force refresh notifications anyway to ensure UI is up-to-date
                    viewModel.refreshNotifications(true);

                    // Update badge even in error case
                    updateMainActivityBadge();
                }
            });
        } catch (Exception e) {
            // Handle any exceptions that might occur
            Log.e(TAG, "Exception marking notification as read: " + e.getMessage(), e);
            ToastUtils.showErrorToast(requireContext(), getString(R.string.error_marking_read));

            // Force refresh notifications to ensure UI is up-to-date
            viewModel.refreshNotifications(true);
        }
    }

    @Override
    public void onNotificationImageClicked(Notification notification) {
        try {
            if (notification == null) {
                Log.e(TAG, "Cannot open image: Notification is null");
                return;
            }

            String imageUrl = notification.getImageUrl();
            if (imageUrl == null || imageUrl.trim().isEmpty()) {
                Log.e(TAG, "Cannot open image: Image URL is null or empty");
                return;
            }

            // Create and navigate to FullScreenImageFragment
            FullScreenImageFragment fullScreenFragment = FullScreenImageFragment.newInstance(
                    imageUrl, notification.getTitle());

            // Replace current fragment with FullScreenImageFragment
            if (getActivity() != null) {
                getActivity().getSupportFragmentManager().beginTransaction()
                        .replace(R.id.fragment_container, fullScreenFragment)
                        .addToBackStack(null)
                        .commit();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error opening full screen image: " + e.getMessage());
            ToastUtils.showErrorToast(requireContext(), "Error opening image: " + e.getMessage());
        }
    }
}
