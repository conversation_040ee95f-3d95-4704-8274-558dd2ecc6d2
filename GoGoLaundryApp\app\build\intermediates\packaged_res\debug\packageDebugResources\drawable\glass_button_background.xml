<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="#66FFFFFF" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
            <item>
                <shape android:shape="rectangle">
                    <stroke 
                        android:width="1dp" 
                        android:color="#80FFFFFF" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- Normal state -->
    <item>
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="#40FFFFFF" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
            <item>
                <shape android:shape="rectangle">
                    <stroke 
                        android:width="1dp" 
                        android:color="#66FFFFFF" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
            <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
                <shape android:shape="rectangle">
                    <stroke 
                        android:width="0.5dp" 
                        android:color="#80FFFFFF" />
                    <corners android:radius="15dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
</selector>
